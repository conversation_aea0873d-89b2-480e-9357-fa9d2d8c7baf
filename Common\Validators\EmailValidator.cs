﻿using Common.Exceptions;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Common.Validators
{
    public class EmailAddressValidator : AbstractValidator<string?>
    {
        public EmailAddressValidator(bool IsRequired)
        {
            if(IsRequired)
            {
                RuleFor(x => x)
                .NotEmpty()
                .WithErrorCode(Errors.Email_Address_Empty.Code)
                .WithMessage(Errors.Email_Address_Empty.Message);
            }

            RuleFor(x => x)
                .EmailAddress()
                .WithErrorCode(Errors.Email_Address_Invalid.Code)
                .WithMessage(Errors.Email_Address_Invalid.Message)
                .MaximumLength(128)
                .WithErrorCode(Errors.Email_Address_MaxLength.Code)
                .WithMessage(Errors.Email_Address_MaxLength.Message);
        }
    }
}
