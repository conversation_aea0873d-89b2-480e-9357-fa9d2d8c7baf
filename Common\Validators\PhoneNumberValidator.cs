﻿using Common.Exceptions;
using FluentValidation;

namespace Common.Validators
{
    public class PhoneNumberValidator : AbstractValidator<string>
    {
        public PhoneNumberValidator()
        {
            RuleFor(x => x)
                .Matches(@"^[0-9]{6,10}$")
                .WithErrorCode(Errors.InvalidPhoneNumber.Code)
                .WithMessage(Errors.InvalidPhoneNumber.Message);
        }
    }
}
