using Common.Models;
using Common.Models.v1;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Geidea.Utils.Security.Claims;

namespace Services.HttpClients;

public class OrderApiClient : IOrderApiClient
{
    private readonly HttpClient httpClient;
    private readonly IOptionsMonitor<UrlSettings> urlOptions;
    private readonly ILogger<OrderApiClient> logger;
    private readonly IHttpContextAccessor contextAccessor;
    
    private string OrderServiceBaseUrl => urlOptions.CurrentValue?.OrderServiceBaseUrl ?? string.Empty;
    
    private const string CreateOrderEndpoint = "/api/v1/orders";
    private const string UpdateOrderEndpoint = "/api/v1/orders";
    private const string UpdateOrderStatusEndpoint = "/api/v1/orders/{0}/status";

    public OrderApiClient(
        HttpClient httpClient, 
        IOptionsMonitor<UrlSettings> urlOptions, 
        ILogger<OrderApiClient> logger,
        IHttpContextAccessor contextAccessor)
    {
        this.httpClient = httpClient;
        this.urlOptions = urlOptions;
        this.logger = logger;
        this.contextAccessor = contextAccessor;
    }

    public async Task<string> CreateOrderAsync(Merchant order)
    {
        var requestUri = $"{OrderServiceBaseUrl}{CreateOrderEndpoint}";
        
        using (logger.BeginScope("CreateOrderAsync({@requestUri})", requestUri))
        {
            logger.LogInformation("Calling Order service to create order {@merchantId}", order.MerchantId);
            
            var requestBody = new StringContent(
                JsonConvert.SerializeObject(order), 
                Encoding.UTF8, 
                "application/json");
                
            // Add correlation ID header
            if (contextAccessor.HttpContext != null)
            {
                var correlationId = contextAccessor.GetCorrelationId();
                if (correlationId.HasValue)
                {
                    httpClient.DefaultRequestHeaders.Remove("X-Correlation-ID");
                    httpClient.DefaultRequestHeaders.Add("X-Correlation-ID", correlationId.Value.ToString());
                }
            }
            
            var response = await httpClient.PostAsync(requestUri, requestBody);
            var jsonResult = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Error when calling Order service to create order. Error was {StatusCode} {@responseBody}", 
                    (int)response.StatusCode, jsonResult);
                throw new PassthroughException(response);
            }

            logger.LogInformation("Successfully created order {@merchantId}", order.MerchantId);
            return jsonResult;
        }
    }

    public async Task<string> UpdateOrderAsync(Order order)
    {
        var requestUri = $"{OrderServiceBaseUrl}{UpdateOrderEndpoint}/{order.MerchantId}";
        
        using (logger.BeginScope("UpdateOrderAsync({@requestUri})", requestUri))
        {
            logger.LogInformation("Calling Order service to update order {@merchantId}", order.MerchantId);
            
            var requestBody = new StringContent(
                JsonConvert.SerializeObject(order), 
                Encoding.UTF8, 
                "application/json");
                
            // Add correlation ID header
            if (contextAccessor.HttpContext != null)
            {
                var correlationId = contextAccessor.GetCorrelationId();
                if (correlationId.HasValue)
                {
                    httpClient.DefaultRequestHeaders.Remove("X-Correlation-ID");
                    httpClient.DefaultRequestHeaders.Add("X-Correlation-ID", correlationId.Value.ToString());
                }
            }
            
            var response = await httpClient.PutAsync(requestUri, requestBody);
            var jsonResult = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Error when calling Order service to update order. Error was {StatusCode} {@responseBody}", 
                    (int)response.StatusCode, jsonResult);
                throw new PassthroughException(response);
            }

            logger.LogInformation("Successfully updated order {@merchantId}", order.MerchantId);
            return jsonResult;
        }
    }

    public async Task<string> UpdateOrderStatusAsync(Order order)
    {
        var requestUri = $"{OrderServiceBaseUrl}{string.Format(UpdateOrderStatusEndpoint, order.MerchantId)}";
        
        using (logger.BeginScope("UpdateOrderStatusAsync({@requestUri})", requestUri))
        {
            logger.LogInformation("Calling Order service to update order status {@merchantId}", order.MerchantId);
            
            var requestBody = new StringContent(
                JsonConvert.SerializeObject(order), 
                Encoding.UTF8, 
                "application/json");
                
            // Add correlation ID header
            if (contextAccessor.HttpContext != null)
            {
                var correlationId = contextAccessor.GetCorrelationId();
                if (correlationId.HasValue)
                {
                    httpClient.DefaultRequestHeaders.Remove("X-Correlation-ID");
                    httpClient.DefaultRequestHeaders.Add("X-Correlation-ID", correlationId.Value.ToString());
                }
            }
            
            var response = await httpClient.PatchAsync(requestUri, requestBody);
            var jsonResult = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Error when calling Order service to update order status. Error was {StatusCode} {@responseBody}", 
                    (int)response.StatusCode, jsonResult);
                throw new PassthroughException(response);
            }

            logger.LogInformation("Successfully updated order status {@merchantId}", order.MerchantId);
            return jsonResult;
        }
    }
}
