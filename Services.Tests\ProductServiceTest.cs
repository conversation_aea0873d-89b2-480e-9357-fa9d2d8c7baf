﻿using AutoMapper;
using Common.Models;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using Services.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Tests
{
    public class ProductServiceTest
    {
        private ProductService messageClient;
        private readonly IProductPublisher msg = Substitute.For<IProductPublisher>();
        private readonly IRequestLogService req = Substitute.For<IRequestLogService>();
        private readonly IMapper mapper = Substitute.For<IMapper>();
        private readonly IRequestLogService reuestlogService = Substitute.For<IRequestLogService>();
        private readonly IHttpContextAccessor accessor = Substitute.For<IHttpContextAccessor>();
        private readonly ILogger<ProductService> logger = Substitute.For<ILogger<ProductService>>();
        public ProductServiceTest()
        {

            messageClient = new ProductService(mapper,msg,req,logger, accessor);
        }

        [Test]
        public Task CreateMessage_WhenMerchantCreateTriggered_ShouldThrowExceptionAsync()
        {
            var merchantCreate = new Common.Models.Product    
            {
                MerchantId ="1234567787"
               

            };

            var result = Assert.ThrowsAsync<ServiceException>(async () => await messageClient.CreateProductMessageAsync(merchantCreate));

            Assert.NotNull(result);
            return Task.CompletedTask;
        }

        [Test]
        public async Task UpdateMessage_WhenProductUpdateTriggered_ShouldTriggerEventAsync()
        {
            var productUpdate = new Product
            {
                MerchantId = "123",

            };

            var result = await messageClient.UpdateProductMessageAsync(productUpdate);
            Assert.NotNull(result);

        }

        [Test]
        public async Task UpdateStatusMessage_WhenProductUpdateStatusTriggered_ShouldTriggerEventAsync()
        {
            var updateStatus = new Product
            {
                MerchantId = "123",

            };

            var result = await messageClient.UpdateProductStatusAsync(updateStatus);
            Assert.NotNull(result);

        }
        [Test]
        public async Task SendFile_WhenProductSendFileTriggered_ShouldTriggerEventAsync()
        {
            var sendFile = new Product
            {
                MerchantId = "123",

            };

            var result = await messageClient.SendFileMessageAsync(sendFile);
            Assert.NotNull(result);

        }
    }
}
