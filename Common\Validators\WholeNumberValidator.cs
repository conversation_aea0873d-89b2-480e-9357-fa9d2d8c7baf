﻿using Common.Exceptions;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Validators
{
    public class WholeNumberValidator : AbstractValidator<string>
    {
        public WholeNumberValidator(bool IsRequired, string FieldName)
        {
            if(IsRequired)
            {
                RuleFor(x => x)
                .NotEmpty()
                .WithErrorCode(Errors.TextRequired.Code)
                .WithMessage(FieldName + " " + Errors.TextRequired.Message);
            }
            RuleFor(x => x)
                .Matches(@"[^0-9]")
                .WithErrorCode(FieldName + " " + Errors.InvalidWholeNumber.Code)
                .WithMessage(Errors.InvalidWholeNumber.Message);

        }
    }
}