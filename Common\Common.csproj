﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <CheckForOverflowUnderflow>True</CheckForOverflowUnderflow>
    <ProjectGuid>{5f3e185e-47cb-4c55-922e-d42e714b413d}</ProjectGuid>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
		  <FrameworkReference Include="Microsoft.AspNetCore.App" />
		  <PackageReference Include="DinkToPdf" Version="1.0.8" />
		  <PackageReference Include="FluentValidation" Version="11.4.0" />
		  <PackageReference Include="Geidea.Messages" Version="2020.8.3.3" />
		  <PackageReference Include="Geidea.Messaging" Version="2.2.238" />
		  <PackageReference Include="Geidea.Utils.ConditionalSerialization" Version="1.0.96" />
		  <PackageReference Include="Geidea.Utils.Counterparty" Version="2.0.232" />
	      <PackageReference Include="Geidea.Utils.ReferenceData" Version="1.0.110" />
		  <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
		  <PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="6.0.12" />	  
  </ItemGroup>

</Project>
