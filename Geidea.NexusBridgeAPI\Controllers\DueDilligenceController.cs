﻿using Common.Services;
using Geidea.Utils.Policies.Evaluation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Geidea.NexusBridgeAPI.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]

public class DueDilligenceController : ControllerBase
{
    private readonly Authorized authorized;
    private readonly ILogger<DueDilligenceController> logger;
    private readonly IDueDilligenceService dueDilligenceService;
    public DueDilligenceController(Authorized authorized, ILogger<DueDilligenceController> logger, IDueDilligenceService dueDilligenceService)
    {
        this.logger = logger;
        this.authorized = authorized;
        this.dueDilligenceService = dueDilligenceService;
    }
}