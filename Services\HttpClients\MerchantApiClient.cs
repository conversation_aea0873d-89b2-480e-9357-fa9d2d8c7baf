using Common.Models.v1;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Geidea.Utils.Security.Claims;

namespace Services.HttpClients;

public class MerchantApiClient : IMerchantApiClient
{
    private readonly HttpClient httpClient;
    private readonly IOptionsMonitor<UrlSettings> urlOptions;
    private readonly ILogger<MerchantApiClient> logger;
    private readonly IHttpContextAccessor contextAccessor;
    
    private string MerchantServiceBaseUrl => urlOptions.CurrentValue?.MerchantServiceBaseUrl ?? string.Empty;
    
    private const string CreateMerchantEndpoint = "/api/v1/merchants";
    private const string UpdateMerchantEndpoint = "/api/v1/merchants";
    private const string UpdateMerchantStatusEndpoint = "/api/v1/merchants/{0}/status";

    public MerchantApiClient(
        HttpClient httpClient, 
        IOptionsMonitor<UrlSettings> urlOptions, 
        ILogger<MerchantApiClient> logger,
        IHttpContextAccessor contextAccessor)
    {
        this.httpClient = httpClient;
        this.urlOptions = urlOptions;
        this.logger = logger;
        this.contextAccessor = contextAccessor;
    }

    public async Task<string> CreateMerchantAsync(Merchant merchant)
    {
        var requestUri = $"{MerchantServiceBaseUrl}{CreateMerchantEndpoint}";
        
        using (logger.BeginScope("CreateMerchantAsync({@requestUri})", requestUri))
        {
            logger.LogInformation("Calling Merchant service to create merchant {@merchantId}", merchant.MerchantId);
            
            var requestBody = new StringContent(
                JsonConvert.SerializeObject(merchant), 
                Encoding.UTF8, 
                "application/json");
                
            // Add correlation ID header
            if (contextAccessor.HttpContext != null)
            {
                var correlationId = contextAccessor.GetCorrelationId();
                if (correlationId.HasValue)
                {
                    httpClient.DefaultRequestHeaders.Remove("X-Correlation-ID");
                    httpClient.DefaultRequestHeaders.Add("X-Correlation-ID", correlationId.Value.ToString());
                }
            }
            
            var response = await httpClient.PostAsync(requestUri, requestBody);
            var jsonResult = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Error when calling Merchant service to create merchant. Error was {StatusCode} {@responseBody}", 
                    (int)response.StatusCode, jsonResult);
                throw new PassthroughException(response);
            }

            logger.LogInformation("Successfully created merchant {@merchantId}", merchant.MerchantId);
            return jsonResult;
        }
    }

    public async Task<string> UpdateMerchantAsync(Merchant merchant)
    {
        var requestUri = $"{MerchantServiceBaseUrl}{UpdateMerchantEndpoint}/{merchant.MerchantId}";
        
        using (logger.BeginScope("UpdateMerchantAsync({@requestUri})", requestUri))
        {
            logger.LogInformation("Calling Merchant service to update merchant {@merchantId}", merchant.MerchantId);
            
            var requestBody = new StringContent(
                JsonConvert.SerializeObject(merchant), 
                Encoding.UTF8, 
                "application/json");
                
            // Add correlation ID header
            if (contextAccessor.HttpContext != null)
            {
                var correlationId = contextAccessor.GetCorrelationId();
                if (correlationId.HasValue)
                {
                    httpClient.DefaultRequestHeaders.Remove("X-Correlation-ID");
                    httpClient.DefaultRequestHeaders.Add("X-Correlation-ID", correlationId.Value.ToString());
                }
            }
            
            var response = await httpClient.PutAsync(requestUri, requestBody);
            var jsonResult = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Error when calling Merchant service to update merchant. Error was {StatusCode} {@responseBody}", 
                    (int)response.StatusCode, jsonResult);
                throw new PassthroughException(response);
            }

            logger.LogInformation("Successfully updated merchant {@merchantId}", merchant.MerchantId);
            return jsonResult;
        }
    }

    public async Task<string> UpdateMerchantStatusAsync(Guid merchantId)
    {
        var requestUri = $"{MerchantServiceBaseUrl}{string.Format(UpdateMerchantStatusEndpoint, merchantId)}";
        
        using (logger.BeginScope("UpdateMerchantStatusAsync({@requestUri})", requestUri))
        {
            logger.LogInformation("Calling Merchant service to update merchant status {@merchantId}", merchantId);
            
            // Add correlation ID header
            if (contextAccessor.HttpContext != null)
            {
                var correlationId = contextAccessor.GetCorrelationId();
                if (correlationId.HasValue)
                {
                    httpClient.DefaultRequestHeaders.Remove("X-Correlation-ID");
                    httpClient.DefaultRequestHeaders.Add("X-Correlation-ID", correlationId.Value.ToString());
                }
            }
            
            var response = await httpClient.PatchAsync(requestUri, null);
            var jsonResult = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Error when calling Merchant service to update merchant status. Error was {StatusCode} {@responseBody}", 
                    (int)response.StatusCode, jsonResult);
                throw new PassthroughException(response);
            }

            logger.LogInformation("Successfully updated merchant status {@merchantId}", merchantId);
            return jsonResult;
        }
    }
}
