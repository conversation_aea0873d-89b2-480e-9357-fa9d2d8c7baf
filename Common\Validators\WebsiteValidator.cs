﻿using Common.Exceptions;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Common.Validators
{
    public class WebsiteValidator : AbstractValidator<string?>
    {
        public WebsiteValidator()
        {
            RuleFor(x => x).Matches(@"(?:^)\+[0-9]{1,3}(?:$)")
                .WithErrorCode(Errors.URL_Invalid.Code).WithMessage(Errors.URL_Invalid.Message);
        }
    }
}
