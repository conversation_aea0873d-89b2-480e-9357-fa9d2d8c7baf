﻿using AutoMapper;
using Common.Data;
using Common.Models;
using Common.Models.v1;
using Common.Services;
using Geidea.Utils.Security.Claims;
using Microsoft.AspNetCore.Http;
using Services.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Geidea.Utils.Common;
using Newtonsoft.Json;
using System.Text;
using System.Reflection.Metadata;
using Microsoft.Extensions.Logging;
using System.Linq;

namespace Services;

public class MerchantService : IMerchantService
{
    private readonly IMapper mapper;
    private readonly IMerchantApiClient merchantApiClient;
    private readonly IRequestLogService requestLogService;
    private readonly IHttpContextAccessor contextAccessor;
    private readonly ILogger<MerchantService> logger;

public MerchantService(IMapper mapper, IMerchantApiClient merchantApiClient, IRequestLogService requestLogService, IHttpContextAccessor contextAccessor, ILogger<MerchantService> logger)
    {
        this.mapper = mapper;
        this.merchantApiClient = merchantApiClient;
        this.requestLogService = requestLogService;
        this.contextAccessor = contextAccessor;
        this.logger = logger;
    }

    public async Task<string> CreateMerchantMessageAsync(Merchant merchant)
    {
        logger.LogInformation("Processing Merchant create action in NexusBridgeAPI for {@merchantId}", merchant.MerchantId);

        var details = mapper.Map<MerchantDetails>(merchant.MerchantDetails);
        var config = mapper.Map<MerchantAccountConfig>(merchant.AccountConfig);
        var externalProduct = mapper.Map<IReadOnlyCollection<MerchantExternalProduct>>(merchant.ExternalProducts);
        var address = mapper.Map<IReadOnlyCollection<MerchantAddress>>(merchant.Addresses);
        var commission = mapper.Map<IReadOnlyCollection<MerchantCommissionConfig>>(merchant.CommissionTypes);
        var bank = mapper.Map<IReadOnlyCollection<MerchantBankAccountResponse>>(merchant.BankAccounts);
        var terminal = mapper.Map<IReadOnlyCollection<MerchantTerminal>>(merchant.MerchantTerminals);
        var person = mapper.Map<IReadOnlyCollection<MerchantPersonOfInterest>>(merchant.PersonOfInterests);
        var fees = mapper.Map<IReadOnlyCollection<MerchantFee>>(merchant.MerchantFees);
        var newCommission = commission.Where(x => !string.IsNullOrEmpty(x.CommissionType)).ToList();

        var request = new Merchant
        {
            Counterparty = merchant.Counterparty,
            LeadId = merchant.LeadId,
            MerchantId = merchant.MerchantId,
            MerchantStatus = merchant.MerchantStatus,
            MerchantType = merchant.MerchantType,
            Tag = merchant.Tag,
            MerchantDetails = details,
            AccountConfig = config,
            Addresses = address,
            ExternalProducts = externalProduct,
            CommissionTypes = newCommission,
            BankAccounts = bank,
            MerchantTerminals = terminal,
            PersonOfInterests = person,
            MerchantFees = fees
        };

        var payload = JsonConvert.SerializeObject(request);
        logger.LogInformation("Sending HTTP request to Merchant API for create action in NexusBridgeAPI for {@merchantId}, Message Payload {@payload}", merchant.MerchantId, payload);

        var apiResponse = await merchantApiClient.CreateMerchantAsync(request);
        await requestLogService.CreateRequestLog(new RequestLog()
        {
            CorrelationId = contextAccessor.GetCorrelationId(),
            ParentCorrelationId = Guid.Empty,
            Entity = Common.Constants.Entity.Merchant,
            EntityId = merchant.MerchantDetails.BusinessId!,
            EntryDate = DateTime.Now,
            RequestId = string.Empty,
            Status = Common.Constants.MerchantEnablementStatus.Published,
            RequestMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(merchant))),
            ResponseMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(apiResponse)),
            RequestType = Common.Constants.Actions.Create,
            UserId = GetCurrentUserId().ToString(),
        });

        return "Merchant create request sent to Merchant API successfully";
    }

    public async Task<string> UpdateMerchantMessageAsync(Merchant merchant)
    {
        var details = mapper.Map<MerchantDetails>(merchant.MerchantDetails);
        var config = mapper.Map<MerchantAccountConfig>(merchant.AccountConfig);
        var externalProduct = mapper.Map<IReadOnlyCollection<MerchantExternalProduct>>(merchant.ExternalProducts);
        var address = mapper.Map<IReadOnlyCollection<MerchantAddress>>(merchant.Addresses);
        var commission = mapper.Map<IReadOnlyCollection<MerchantCommissionConfig>>(merchant.CommissionTypes);
        var bank = mapper.Map<IReadOnlyCollection<MerchantBankAccountResponse>>(merchant.BankAccounts);


        var request = new Merchant
        {
            Counterparty = merchant.Counterparty,
            LeadId = merchant.LeadId,
            MerchantId = merchant.MerchantId,
            MerchantStatus = merchant.MerchantStatus,
            MerchantType = merchant.MerchantType,
            Tag = merchant.Tag,
            MerchantDetails = details,
            AccountConfig = config,
            Addresses = address,
            ExternalProducts = externalProduct,
            CommissionTypes = commission,
            BankAccounts = bank

        };

        var apiResponse = await merchantApiClient.UpdateMerchantAsync(request);
        await requestLogService.CreateRequestLog(new RequestLog()
        {
            CorrelationId = contextAccessor.GetCorrelationId(),
            ParentCorrelationId = Guid.Empty,
            Entity = Common.Constants.Entity.Merchant,
            EntityId = string.Empty,
            EntryDate = DateTime.Now,
            RequestId = string.Empty,
            Status = Common.Constants.MerchantEnablementStatus.InProgress,
            RequestMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(request))),
            ResponseMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(apiResponse)),
            RequestType = Common.Constants.Actions.Update,
            UserId = GetCurrentUserId().ToString(),
        }) ;
        return "Merchant update request sent to Merchant API successfully";
    }

    public async Task<string> UpdateMerchantStatusMessageAsync(Guid merchantId)
    {
        var apiResponse = await merchantApiClient.UpdateMerchantStatusAsync(merchantId);
        await requestLogService.CreateRequestLog(new RequestLog()
        {
            CorrelationId = contextAccessor.GetCorrelationId(),
            ParentCorrelationId = Guid.Empty,
            Entity = Common.Constants.Entity.Merchant,
            EntityId = string.Empty,
            EntryDate = DateTime.Now,
            RequestId = string.Empty,
            Status = Common.Constants.MerchantEnablementStatus.InProgress,
            RequestMetaData = string.Empty,
            ResponseMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(apiResponse)),
            RequestType = Common.Constants.Actions.Create,
            UserId = GetCurrentUserId().ToString(),
        });
        return "Merchant status update request sent to Merchant API successfully";
    }

    private Guid GetCurrentUserId()
    {
        return ClaimsPrincipalExtensions.GetUserIdFromClaims(contextAccessor.HttpContext!.User.Claims);
    }

}