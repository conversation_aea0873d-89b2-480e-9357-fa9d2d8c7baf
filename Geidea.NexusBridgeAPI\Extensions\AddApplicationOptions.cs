﻿using Common.Options;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Geidea.NexusBridgeAPI.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddApplicationOptions(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddOptions();

        services.AddOptions<UrlSettings>().Bind(configuration.GetSection("UrlSettings"))
            .Validate(o =>
            {
                return true;
            });

        services.AddOptions<ApplicationOptions>().Bind(configuration.GetSection("Application"));
        services.AddOptions<ExceptionOptions>().Bind(configuration.GetSection("ApiExceptions"));
        services.AddOptions<ContractSettings>().Bind(configuration.GetSection("ContractSettings"));

        return services;
    }
}