﻿using Common.Models.v1;
using System;

namespace Services.Messaging;

public interface IMerchantPublisher
{
    void PublishMerchantCreatedEvent(Merchant merchant);
    void PublishMerchantUpdatedEvent(Merchant merchant);
    void PublishMerchantUpdateStatusEvent(Guid merchantId);
    //void PublishMerchantAddressCreatedEvent(MerchantAddress merchantAddress);
    //void PublishMerchantAddressUpdatedEvent(MerchantAddress merchantAddress);
    //void PublishMerchantAddressDeletedEvent(Guid merchantId, Guid addressId, string? purpose);
    //void PublishMeezaMerchantRegistrationEvent(IReadOnlyCollection<Guid> storeIds, Guid merchantId, string counterparty);
    //void PublishStoreGatewayConfigurationCreateEvent(Guid storeId, Guid merchantId, string merchantName, string merchantStatus, string counterParty);
}