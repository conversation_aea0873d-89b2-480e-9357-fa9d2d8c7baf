﻿using Common.Exceptions;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Common.Validators
{
    public class TextValidator: AbstractValidator<string>
    {
        public TextValidator(bool IsRequired, string FieldName, [Optional] int? MaxLength, [Optional] int? MinLength)
        {
            if(IsRequired)
            {
                RuleFor(x => x)
                .NotEmpty()
                .WithErrorCode(Errors.TextRequired.Code)
                .WithMessage(FieldName + " " + Errors.TextRequired.Message);
            }

            if (MaxLength > 0 && MaxLength != null)
            {
                RuleFor(x => x)
                .MaximumLength(Convert.ToInt32(MaxLength))
                .WithErrorCode(Errors.TextMaxLength.Code)
                .WithMessage(FieldName + " " + Errors.TextMaxLength.Message + " " + MaxLength);
            }

            if (MinLength != 0 && MinLength != null)
            {
                RuleFor(x => x)
                .MaximumLength(Convert.ToInt32(MinLength))
                .WithErrorCode(Errors.TextMinLength.Code)
                .WithMessage(FieldName + " " + Errors.TextMinLength.Message + " " + MinLength);
            }
        }
    }
}
