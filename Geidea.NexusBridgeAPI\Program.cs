using Geidea.PaymentGateway.ConfigServiceClient;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using System;

namespace Geidea.NexusBridgeAPI;

public static class Program
{
    public static void Main(string[] args)
    {
        try
        {
            CreateHostBuilder(args).Build().Run();
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.ToString());
            throw;
        }
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureWebHostDefaults(webBuilder =>
            {
                webBuilder.UseStartup<Startup>();
                if (!string.IsNullOrEmpty(Environment.GetEnvironmentVariable("IsRunInEnvironments")))
                {
                    webBuilder.UseConfigService();
                }
            });
}