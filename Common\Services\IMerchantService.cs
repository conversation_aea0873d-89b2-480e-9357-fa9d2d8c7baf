﻿using Common.Models;
using Common.Models.v1;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services;

public interface IMerchantService
{
    Task<string> CreateMerchantMessageAsync(Merchant merchant);
    Task<string> UpdateMerchantMessageAsync(Merchant merchant);
    Task<string> UpdateMerchantStatusMessageAsync(Guid request);
    //Task<string> CreateMerchantAddressMessageAsync(MerchantAddress address);
    //Task<string> UpdateMerchantAddressMessageAsync(MerchantAddress address);
    //Task<string> DeleteMerchantAddressMessageAsync(MerchantAddressDeleteRequest request);
    //Task<string> PublishMeezaMerchantRegistrationEvent(IReadOnlyCollection<Guid> storeIds, Guid merchantId, string counterparty);
    //Task<string> PublishStoreGatewayConfigurationCreateEvent(StoreGatewayConfig config);
}