﻿using AutoMapper;
using Common.Models;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using Services.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Tests
{
    public class OrderServiceTest
    {
        private OrderService messageClient;
        private readonly IMapper contextAccessor = Substitute.For<IMapper>();
        private readonly IOrderPublisher msg = Substitute.For<IOrderPublisher>();
        private readonly IMapper mapper = Substitute.For<IMapper>();
        private readonly IRequestLogService reuestlogService = Substitute.For<IRequestLogService>();
        private readonly IHttpContextAccessor accessor = Substitute.For<IHttpContextAccessor>();
        private readonly ILogger<OrderService> logger = Substitute.For<ILogger<OrderService>>();
        public OrderServiceTest()
        {

            messageClient = new OrderService(mapper,msg, reuestlogService,logger, accessor);
        }

        [Test]
        public Task CreateMessage_WhenMerchantCreateTriggered_ShouldThrowExceptionAsync()
        {
            var merchantCreate = new Common.Models.v1.Merchant
            {
                MerchantId = Guid.NewGuid(),
                LeadId = Guid.NewGuid(),
                MerchantType = "Retail",
                MerchantStatus = "Active",
                Tag = "High-Value",
                Counterparty = "GEIDEA_UAE",
                CreatedBy = "AdminUser",
                UpdatedBy = null,
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = null,
                DeletedFlag = false,
                ApplicationId = "App123"

            };

            //var result= await messageClient.CreateMerchantMessageAsync(merchantCreate);
            var result = Assert.ThrowsAsync<ServiceException>(async () => await messageClient.CreateOrderMessageAsync(merchantCreate));

            Assert.NotNull(result);
            return Task.CompletedTask;
        }

        public async Task OrderUpdate_WhenOrderUpdateTriggered_ShouldTriggerEventAsync()
        {
            var orderCreate = new Order
            {
                AgreementId = Guid.NewGuid(),
                MerchantId = Guid.NewGuid(),
                StoreId = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                AddressId = Guid.NewGuid(),
                OrderStatus = "NEW",
                TrackingNumber = "*********",
                TrackingUrl = "https://tracking-url.com",
                Shipper = "Some Shipper",
                ShippedDate = DateTime.UtcNow,
                CouponCode = "SUMMER10",
                PaymentMethod = "Credit Card",
                CompanyRegNo = "ABC123",
                SalesName = "John Doe",
                SubscriptionPlan = "Premium",
                Note = "This is a test order",
                MerchantName = "MerchantABC",
                Subtotal = 100,
                Discount = 10,
                VatPercent = 20,
                Vat = 18,
                Total = 108,
                MonthlySubtotal = 80,
                MonthlyDiscount = 8,
                MonthlyVat = 16,
                MonthlyTotal = 88,
                YearlySubtotal = 960,
                YearlyDiscount = 96,
                YearlyVat = 192,
                YearlyTotal = 1056,
                Currency = "USD",
                DeliveryMethod = "Express",
                DeliveryDays = 3,
                ProofOfDelivery = true,
                BillPaymentsStatus = "Paid",
                MigrationRequest = false

            };

            var result = await messageClient.UpdateOrderMessageAsync(orderCreate);
            Assert.NotNull(result);

        }
        public async Task UpdateStatus_WhenOrderUpdateStatusTriggered_ShouldTriggerEventAsync()
        {
            var orderCreate = new Order
            {
                AgreementId = Guid.NewGuid(),
                MerchantId = Guid.NewGuid(),
                StoreId = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                AddressId = Guid.NewGuid(),
                OrderStatus = "NEW",
                TrackingNumber = "*********",
                TrackingUrl = "https://tracking-url.com",
                Shipper = "Some Shipper",
                ShippedDate = DateTime.UtcNow,
                CouponCode = "SUMMER10",
                PaymentMethod = "Credit Card",
                CompanyRegNo = "ABC123",
                SalesName = "John Doe",
                SubscriptionPlan = "Premium",
                Note = "This is a test order",
                MerchantName = "MerchantABC",
                Subtotal = 100,
                Discount = 10,
                VatPercent = 20,
                Vat = 18,
                Total = 108,
                MonthlySubtotal = 80,
                MonthlyDiscount = 8,
                MonthlyVat = 16,
                MonthlyTotal = 88,
                YearlySubtotal = 960,
                YearlyDiscount = 96,
                YearlyVat = 192,
                YearlyTotal = 1056,
                Currency = "USD",
                DeliveryMethod = "Express",
                DeliveryDays = 3,
                ProofOfDelivery = true,
                BillPaymentsStatus = "Paid",
                MigrationRequest = false

            };
            var result = await messageClient.UpdateStatusAsync(orderCreate);
            Assert.NotNull(result);

        }
    }
}
