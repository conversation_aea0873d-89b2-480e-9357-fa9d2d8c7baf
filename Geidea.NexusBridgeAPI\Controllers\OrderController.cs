﻿using Common.Models;
using Common.Models.v1;
using Common.Services;
using Geidea.Utils.Policies.Evaluation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Services;
using System.Threading.Tasks;

namespace Geidea.NexusBridgeAPI.Controllers;

[Authorize]
[ApiController]
[Route("api/v1/[controller]")]
public class OrderController : ControllerBase
{
    private readonly Authorized authorized;
    private readonly IOrderService orderService;
    private readonly ILogger<OrderController> logger;

    public OrderController(Authorized authorized, ILogger<OrderController> logger, IOrderService orderService)
    {
        this.logger = logger;
        this.authorized = authorized;
        this.orderService = orderService;
    }

    /// <summary>
    /// Create Order Message
    /// </summary>
    /// <response code="200">Returns a successful message sent</response>
    /// <response code="400">Returns the error.</response>
    /// <response code="401">If the request in unauthorized.</response>
    /// <response code="403">If the user does not have the correct role.</response>
    [HttpPost("create")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> CreateOrder([FromBody] Merchant order)
    {
        var result = await orderService.CreateOrderMessageAsync(order);
        return Ok(result);
    }

    /// <summary>
    /// Update Order Message
    /// </summary>
    /// <response code="200">Returns a successful message sent</response>
    /// <response code="400">Returns the error.</response>
    /// <response code="401">If the request in unauthorized.</response>
    /// <response code="403">If the user does not have the correct role.</response>
    [HttpPatch("update")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> UpdateOrder([FromBody] Order order)
    {
        var result = await orderService.UpdateOrderMessageAsync(order);
        return Ok(result);
    }

    /// <summary>
    /// Update Status Message
    /// </summary>
    /// <response code="200">Returns a successful message sent</response>
    /// <response code="400">Returns the error.</response>
    /// <response code="401">If the request in unauthorized.</response>
    /// <response code="403">If the user does not have the correct role.</response>
    [HttpPatch("updatestatus")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> UpdateStatus([FromBody] Order order)
    {
        var result = await orderService.UpdateStatusAsync(order);
        return Ok(result);
    }
}