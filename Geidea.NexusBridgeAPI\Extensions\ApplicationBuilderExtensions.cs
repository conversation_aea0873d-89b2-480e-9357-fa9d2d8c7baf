﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Services.Messaging;

namespace Geidea.NexusBridgeAPI.Extensions;

public static class ApplicationBuilderExtensions
{
    public static IApplicationBuilder StartMessageConsumption(this IApplicationBuilder builder)
    {
        var merchantPublisher = builder.ApplicationServices.GetRequiredService<MerchantPublisher>();
        merchantPublisher.Connect();
        var orderPublisher = builder.ApplicationServices.GetService<OrderPublisher>();
        orderPublisher?.Connect();
        var productpublisher = builder.ApplicationServices.GetService<ProductPublisher>();
        productpublisher?.Connect();
        return builder;
    }
}