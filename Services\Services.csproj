﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<LangVersion>latest</LangVersion>
		<Nullable>enable</Nullable>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<CheckForOverflowUnderflow>True</CheckForOverflowUnderflow>
		<ProjectGuid>{6ec77ef4-65dd-4bc5-a09f-ef3bf22164c0}</ProjectGuid>
    <LangVersion>latest</LangVersion>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="12.0.0" />
		<PackageReference Include="Geidea.Utils.Cleanup" Version="1.0.2" />
		<PackageReference Include="Geidea.Utils.Json" Version="1.1.249" />
		<PackageReference Include="Geidea.Utils.Security" Version="1.0.1" />
		<PackageReference Include="Geidea.Utils.Validation" Version="2.1.114" />
		<PackageReference Include="Geidea.Utils.WebUtilities" Version="1.0.5" />
		<PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="5.2.9" />
		<PackageReference Include="Microsoft.Extensions.Options" Version="6.0.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Common\Common.csproj" />
		<ProjectReference Include="..\Messaging\Messaging.csproj" />
	</ItemGroup>
</Project>