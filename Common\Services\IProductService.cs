﻿using System.Threading.Tasks;
using System;
using Common.Models;

namespace Common.Services;

public interface IProductService
{
    //Task<ProductResponse> CreateAsync(ProductRequest request);
    //Task BindPartAsync(Guid partId, Guid productId, int? quantity);
    //Task UnbindPartAsync(Guid partId, Guid productId);
    //Task<ProductResponse> PatchAsync(Guid id, JsonPatchDocument<ProductRequest> patchDocument);
    //Task DeleteProductAsync(Guid productId);
    //Task AddCategoryAsync(Guid productId, Guid categoryId);
    //Task DeleteCategoryAsync(Guid productId, Guid categoryId);
    //void ValidateGatewayCardSchemesPerCountry(CreateProductInstanceRequest request, GatewayData gatewayData, string counterpartyCode);
    //void ValidateGatewayCurrencies(IReadOnlyCollection<string> currencies);
    //void ValidateGatewayCardBrandProviders(IReadOnlyCollection<CardBrandProvider> cardBrandProviders, string counterpartyCode);
    //void SetCurrenciesAutomatically(GatewayData gatewayData, string counterpartyCode);
    //void ValidateCountry(string merchantCountry, string counterpartyCode);
    //void SetDefaultCountry(GatewayData gatewayData, string counterpartyCode);
    //void ValidateCardBrands(Guid[] cardIds, string counterpartyCode);
    Task<string> CreateProductMessageAsync(Product product);
    Task<string> UpdateProductMessageAsync(Product product);
    Task<string> UpdateProductStatusAsync(Product product);
    Task<string> SendFileMessageAsync(Product product);
}