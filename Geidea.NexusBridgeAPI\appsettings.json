{"ConfigService": {"Url": "http://uae-dev-config.uae-dev.gd-azure-dev.net", "VaultSecretsPath": "/usr/share/secrets/secrets.json", "Keys": ["ConfigService:VaultSecretsPath", "Application:DefaultContactReasonName", "Serilog:MinimumLevel", "Keycloak:*", "Application:<PERSON>wa<PERSON>", "ApiExceptions:UseSlim", "Polly:TimeoutInSeconds", "AuthorizationPolicy:Server", "AuthorizationPolicy:Namespace", "UrlSettings:MerchantServiceBaseUrl", "UrlSettings:OrderServiceBaseUrl", "UrlSettings:ProductServiceBaseUrl", "Header:Whitelist", "Default:HealthChecks:*", "Csv:*", "UrlSettings:*"]}, "Header": {"Whitelist": "X-Original-For,X-Original-Proto,X-CounterpartyC<PERSON>,X-ApplicationLanguage,X-Correlation-ID,X-Appgw-Trace-Id,X-B3-<PERSON><PERSON><PERSON>,X-B3-<PERSON><PERSON>,X-B3-<PERSON>ni<PERSON>,X-B3-<PERSON><PERSON>,X-Envoy-Attempt-Count,X-Envoy-External-Address,X-Forwarded-Client-Cert,X-Forwarded-Host,X-Forwarded-For,X-Forwarded-Port,X-Forwarded-Proto,X-Original-Host,X-Original-Url,X-Real-Ip,X-Request-Id,X-Envoy-Internal,X-Consumer-Username,X-Credential-Identifier,X-Consumer-Id,X-ENVOY-DECORATOR-OPERATION,X-ENVOY-PEER-METADATA,X-ENVOY-PEER-METADATA-ID,X-FORWARDED-PATH,X-REQUESTED-WITH,X-BLUECOAT-VIA"}, "UrlSettings": {"RequestLogServiceBaseUrl": "http://uae-dev-log.uae-dev.gd-azure-dev.net", "MerchantServiceBaseUrl": "http://uae-dev-merchant.uae-dev.gd-azure-dev.net", "OrderServiceBaseUrl": "http://uae-dev-order.uae-dev.gd-azure-dev.net", "ProductServiceBaseUrl": "http://uae-dev-product.uae-dev.gd-azure-dev.net"}, "Application": {"Name": "Geidea.NexusBridgeAPI", "Version": "0.1.0", "DefaultContactReasonName": "DEFAULT"}, "AuthorizationPolicy": {"Server": "http://uae-dev-openpolicy.uae-dev.gd-azure-dev.net/v1/data", "Namespace": "portal"}, "Csv": {"Separator": ",", "ProductType": "TERMINAL", "ReferenceCatalogue": "MERCHANT_CATEGORY_CODE"}, "AllowedHosts": "*", "Keycloak": {"Authority": "https://api-uae-dev.gd-azure-dev.net/auth/realms/uae-dev", "RequireHttpsMetadata": false, "PublicKey": "MIIClTCCAX0CBgFwC2lhnDANBgkqhkiG9w0BAQsFADAOMQwwCgYDVQQDDANkZXYwHhcNMjAwMjAzMTQxNTIwWhcNMzAwMjAzMTQxNzAwWjAOMQwwCgYDVQQDDANkZXYwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCMCUG9nCz7FdrkTIK3lijxnX22I3InVxqijDlJoQMOkiYcmdCm0WVFBsKcJlsysKUAsmSL8p7tv9YpaYF/6DmU80h0SqwBPdK4aQMTr1n1sGawkUGi95RWOVs6SP7aR+e8VHmSRaZ1gUy2GouGYafqMHyZiVGZDcSWHiPXIPXCvUDa9jlEKFSpaGfXCJ3nlh0G17LfbA0K6kwd+vWapSAHjr12XWgqWBC3INGIYG3hbxLGSgpcElFh7aE6YxEg8PTfJpluSG9s5QZze5rhEc4umYciY4iIEHzyoCpi8Hn2BXcEYxnZmXTCP0Qm4FuaK50/YZ8AyN/rjnuNquknWQFbAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHLS35idfXu0i3yLUWET4MSRB/W51DKJDRPcZvWoQDibsjZUMgK9gXH8DJXfyyTs6zW7mziZgA49u2szibyqDRtGVZ4BJtIcbxAQfcOXoRkjyoBtqr3gd7qlvi+C4ntVmvi0I3LHSFOFUP8own76sjrtCKsE6LY4XlaPMp0e+e0OI37oVDzh5BfuJOumT8nGBT6aA0ul5MlsfT3D1BGtpAnDftbeHjVpg18Hme1i4RnpT5GjFBT07yCVTmqTg1n94WLbi9bXHQo+iE6h9x9XGUlp8bYHSmiWEoGmnmEMn0Io3Sikpy0jegsunascPEyGCIO0nrbJwN6l/qUmEv+RHzk="}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Error", "System": "Error"}}, "Using": ["Serilog.Sinks.Console"], "Enrich": ["FromLogContext", "WithMachineName", "WithEnvironmentUserName", "WithProcessId"], "Properties": {"ApplicationName": "Geidea.NexusBridgeAPI", "ProcessName": "Geidea.NexusBridgeAPI"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}]}, "ApiExceptions": {"UseSlim": true}, "Polly": {"TimeoutInSeconds": 40}}