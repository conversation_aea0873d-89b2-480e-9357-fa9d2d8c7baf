﻿using AutoMapper;
using Common.Models;
using Common.Models.v1;
using Common.Services;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Services.Models;
using System.Collections.Generic;
using System.Text;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Geidea.Utils.Security.Claims;
using System.Linq;

namespace Services;

public class OrderService : IOrderService
{
    private readonly IMapper mapper;
    private readonly IOrderApiClient orderApiClient;
    private readonly IRequestLogService requestLogService;
    private readonly ILogger<OrderService> logger;
    private readonly IHttpContextAccessor contextAccessor;

    public OrderService(IMapper mapper, IOrderApiClient orderApiClient, IRequestLogService requestLogService, ILogger<OrderService> logger, IHttpContextAccessor contextAccessor)
    {
        this.mapper = mapper;
        this.orderApiClient = orderApiClient;
        this.requestLogService = requestLogService;
        this.logger = logger;
        this.contextAccessor = contextAccessor;


    }
    public async Task<string> CreateOrderMessageAsync(Common.Models.v1.Merchant order)
    {
        logger.LogInformation("Processing  Merchant create action in NexusBridgeAPI for {merchantId}", order.MerchantId);

        var details = mapper.Map<Common.Models.MerchantDetails>(order.MerchantDetails);
        var config = mapper.Map<MerchantAccountConfig>(order.AccountConfig);
        var externalProduct = mapper.Map<IReadOnlyCollection<MerchantExternalProduct>>(order.ExternalProducts);
        var address = mapper.Map<IReadOnlyCollection<Common.Models.v1.MerchantAddress>>(order.Addresses);
        var commission = mapper.Map<IReadOnlyCollection<MerchantCommissionConfig>>(order.CommissionTypes);
        var bank = mapper.Map<IReadOnlyCollection<MerchantBankAccountResponse>>(order.BankAccounts);
        var terminal = mapper.Map<IReadOnlyCollection<MerchantTerminal>>(order.MerchantTerminals);
        var person = mapper.Map<IReadOnlyCollection<MerchantPersonOfInterest>>(order.PersonOfInterests);
        var newCommission = commission.Where(x => !string.IsNullOrEmpty(x.CommissionType) && x.Value != 0).ToList();

        var request = new Common.Models.v1.Merchant
        {
            Counterparty = order.Counterparty,
            LeadId = order.LeadId,
            MerchantId = order.MerchantId,
            MerchantStatus = order.MerchantStatus,
            MerchantType = order.MerchantType,
            Tag = order.Tag,
            MerchantDetails = details,
            AccountConfig = config,
            Addresses = address,
            ExternalProducts = externalProduct,
            CommissionTypes = newCommission,
            BankAccounts = bank,
            MerchantTerminals = terminal,
            PersonOfInterests = person,

        };
        logger.LogInformation("Sending HTTP request to Order API for create action in NexusBridgeAPI for {merchantId}", order.MerchantId);

        var apiResponse = await orderApiClient.CreateOrderAsync(request);
        await requestLogService.CreateRequestLog(new RequestLog()
        {
            CorrelationId = contextAccessor.GetCorrelationId(),
            ParentCorrelationId = Guid.Empty,
            Entity = Common.Constants.Entity.Order,
            EntityId = order.MerchantDetails.BusinessId!,
            EntryDate = DateTime.Now,
            RequestId = string.Empty,
            Status = Common.Constants.MerchantEnablementStatus.Published,
            RequestMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(order))),
            ResponseMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(apiResponse)),
            RequestType = Common.Constants.Actions.Create,
            UserId = GetCurrentUserId().ToString(),
        });

        return "Order create request sent to Order API successfully";

    }
    public async Task<string> UpdateOrderMessageAsync(Order order)
    {
        var msg = mapper.Map<Order>(order);

        var apiResponse = await orderApiClient.UpdateOrderAsync(msg);
        return "Update Order request sent to Order API successfully";
    }
    public async Task<string> UpdateStatusAsync(Order order)
    {
        var msg = mapper.Map<Order>(order);

        var apiResponse = await orderApiClient.UpdateOrderStatusAsync(msg);
        return "Update Order status sent to Order API successfully";
    }

    private Guid GetCurrentUserId()
    {
        return ClaimsPrincipalExtensions.GetUserIdFromClaims(contextAccessor.HttpContext!.User.Claims);
    }
}
