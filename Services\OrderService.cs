﻿using AutoMapper;
using Common.Models;
using Common.Models.v1;
using Common.Services;
using Geidea.Messages.Merchant;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Services.Messaging;
using Services.Models;
using System.Collections.Generic;
using System.Text;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Geidea.Utils.Security.Claims;
using System.Linq;

namespace Services;

public class OrderService : IOrderService
{
    private readonly IMapper mapper;
    private readonly IOrderPublisher orderPublisher;
    private readonly IRequestLogService requestLogService;
    private readonly ILogger<OrderService> logger;
    private readonly IHttpContextAccessor contextAccessor;

    public OrderService(IMapper mapper, IOrderPublisher orderPublisher, IRequestLogService requestLogService, ILogger<OrderService> logger, IHttpContextAccessor contextAccessor)
    {
        this.mapper = mapper;
        this.orderPublisher = orderPublisher;
        this.requestLogService = requestLogService;
        this.logger = logger;
        this.contextAccessor = contextAccessor;


    }
    public async Task<string> CreateOrderMessageAsync(Common.Models.v1.Merchant order)
    {
        logger.LogInformation("Processing  Merchant create action in NexusBridgeAPI for {merchantId}", order.MerchantId);

        var details = mapper.Map<Common.Models.MerchantDetails>(order.MerchantDetails);
        var config = mapper.Map<MerchantAccountConfig>(order.AccountConfig);
        var externalProduct = mapper.Map<IReadOnlyCollection<MerchantExternalProduct>>(order.ExternalProducts);
        var address = mapper.Map<IReadOnlyCollection<Common.Models.v1.MerchantAddress>>(order.Addresses);
        var commission = mapper.Map<IReadOnlyCollection<MerchantCommissionConfig>>(order.CommissionTypes);
        var bank = mapper.Map<IReadOnlyCollection<MerchantBankAccountResponse>>(order.BankAccounts);
        var terminal = mapper.Map<IReadOnlyCollection<MerchantTerminal>>(order.MerchantTerminals);
        var person = mapper.Map<IReadOnlyCollection<MerchantPersonOfInterest>>(order.PersonOfInterests);
        var newCommission = commission.Where(x => !string.IsNullOrEmpty(x.CommissionType) && x.Value != 0).ToList();

        var request = new Common.Models.v1.Merchant
        {
            Counterparty = order.Counterparty,
            LeadId = order.LeadId,
            MerchantId = order.MerchantId,
            MerchantStatus = order.MerchantStatus,
            MerchantType = order.MerchantType,
            Tag = order.Tag,
            MerchantDetails = details,
            AccountConfig = config,
            Addresses = address,
            ExternalProducts = externalProduct,
            CommissionTypes = newCommission,
            BankAccounts = bank,
            MerchantTerminals = terminal,
            PersonOfInterests = person,

        };
        logger.LogInformation("Sending message to   RabbitMQ for create action in NexusBridgeAPI for {merchantId}", order.MerchantId);

        await Task.Run(() => orderPublisher.PublishOrderCreatedEvent(request));
        await requestLogService.CreateRequestLog(new RequestLog()
        {
            CorrelationId = contextAccessor.GetCorrelationId(),
            ParentCorrelationId = Guid.Empty,
            Entity = Common.Constants.Entity.Order,
            EntityId = order.MerchantDetails.BusinessId!,
            EntryDate = DateTime.Now,
            RequestId = string.Empty,
            Status = Common.Constants.MerchantEnablementStatus.Published,
            RequestMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(order))),
            ResponseMetaData = string.Empty,
            RequestType = Common.Constants.Actions.Create,
            UserId = GetCurrentUserId().ToString(),
        });

        return " Order create request sent to RabitMQ successfully";

    }
    public async Task<string> UpdateOrderMessageAsync(Order order)
    {
        var msg = mapper.Map<Order>(order);

        await Task.Run(() => orderPublisher.PublishOrderUpdatedEvent(msg));
        return "Update Order request sent to RabitMQ successfully";
    }
    public async Task<string> UpdateStatusAsync(Order order)
    {
        var msg = mapper.Map<Order>(order);

        await Task.Run(() => orderPublisher.PublishUpdateStatusEvent(msg));
        return "Update Order status sent to RabitMQ successfully";
    }

    private Guid GetCurrentUserId()
    {
        return ClaimsPrincipalExtensions.GetUserIdFromClaims(contextAccessor.HttpContext!.User.Claims);
    }
}
