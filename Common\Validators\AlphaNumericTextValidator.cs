﻿using Common.Exceptions;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Common.Validators
{
    public class AlphaNumericTextValidator : AbstractValidator<string>
    {
        public AlphaNumericTextValidator(bool IsRequired, string FieldName, [Optional]int? MaxLength, [Optional]int? MinLength)
        {
            if (IsRequired)
            {
                RuleFor(x => x)
                .NotEmpty()
                .WithErrorCode(Errors.TextRequired.Code)
                .WithMessage(FieldName + " " + Errors.TextRequired.Message);
            }

            //^[a-z\d\-_\s]+$ = Alphanumeric Regex
            RuleFor(x => x)
                .Matches(@"^[a-z\d\-_\s]+$")
                .WithErrorCode(Errors.AlphaNumericFailed.Code)
                .WithMessage(FieldName + " " + Errors.AlphaNumericFailed.Message);
            
            if (MaxLength > 0 && MaxLength != null)
            { 
                RuleFor(x => x)
                .MaximumLength(Convert.ToInt32(MaxLength))
                .WithErrorCode(Errors.TextMaxLength.Code)
                .WithMessage(FieldName + " " + Errors.TextMaxLength.Message + " " + MaxLength);
            }

            if (MinLength != 0 && MinLength != null)
            {
                RuleFor(x => x)
                .MaximumLength(Convert.ToInt32(MinLength))
                .WithErrorCode(Errors.TextMinLength.Code)
                .WithMessage(FieldName + " " + Errors.TextMinLength.Message + " " + MinLength);
            }
        }
    }
}
