﻿using GeideaPaymentGateway.Utils.RabbitMQ;
using RabbitMQ.Client;
using System.Net.Security;

namespace Services.Messaging;

public static class RabbitMqHelper
{
    public static ConnectionFactory CreateConnectionFactory(RabbitMqConfig config)
    {
        return new ConnectionFactory
        {
            HostName = config.HostName,
            Port = config.Port,
            VirtualHost = config.VirtualHost,
            UserName = config.UserName,
            Password = config.Password,
            Ssl = new SslOption
            {
                Enabled = config.SslEnabled,
                ServerName = config.HostName,
                AcceptablePolicyErrors = SslPolicyErrors.RemoteCertificateNotAvailable
            }
        };
    }
}