using Common.Models;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Geidea.Utils.Security.Claims;

namespace Services.HttpClients;

public class ProductApiClient : IProductApiClient
{
    private readonly HttpClient httpClient;
    private readonly IOptionsMonitor<UrlSettings> urlOptions;
    private readonly ILogger<ProductApiClient> logger;
    private readonly IHttpContextAccessor contextAccessor;
    
    private string ProductServiceBaseUrl => urlOptions.CurrentValue?.ProductServiceBaseUrl ?? string.Empty;
    
    private const string CreateProductEndpoint = "/api/v1/products";
    private const string UpdateProductEndpoint = "/api/v1/products";
    private const string UpdateProductStatusEndpoint = "/api/v1/products/{0}/status";
    private const string SendFileEndpoint = "/api/v1/products/{0}/files";

    public ProductApiClient(
        HttpClient httpClient, 
        IOptionsMonitor<UrlSettings> urlOptions, 
        ILogger<ProductApiClient> logger,
        IHttpContextAccessor contextAccessor)
    {
        this.httpClient = httpClient;
        this.urlOptions = urlOptions;
        this.logger = logger;
        this.contextAccessor = contextAccessor;
    }

    public async Task<string> CreateProductAsync(Product product)
    {
        var requestUri = $"{ProductServiceBaseUrl}{CreateProductEndpoint}";
        
        using (logger.BeginScope("CreateProductAsync({@requestUri})", requestUri))
        {
            logger.LogInformation("Calling Product service to create product {@merchantId}", product.MerchantId);
            
            var requestBody = new StringContent(
                JsonConvert.SerializeObject(product), 
                Encoding.UTF8, 
                "application/json");
                
            // Add correlation ID header
            if (contextAccessor.HttpContext != null)
            {
                var correlationId = contextAccessor.GetCorrelationId();
                if (correlationId.HasValue)
                {
                    httpClient.DefaultRequestHeaders.Remove("X-Correlation-ID");
                    httpClient.DefaultRequestHeaders.Add("X-Correlation-ID", correlationId.Value.ToString());
                }
            }
            
            var response = await httpClient.PostAsync(requestUri, requestBody);
            var jsonResult = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Error when calling Product service to create product. Error was {StatusCode} {@responseBody}", 
                    (int)response.StatusCode, jsonResult);
                throw new PassthroughException(response);
            }

            logger.LogInformation("Successfully created product {@merchantId}", product.MerchantId);
            return jsonResult;
        }
    }

    public async Task<string> UpdateProductAsync(Product product)
    {
        var requestUri = $"{ProductServiceBaseUrl}{UpdateProductEndpoint}/{product.MerchantId}";
        
        using (logger.BeginScope("UpdateProductAsync({@requestUri})", requestUri))
        {
            logger.LogInformation("Calling Product service to update product {@merchantId}", product.MerchantId);
            
            var requestBody = new StringContent(
                JsonConvert.SerializeObject(product), 
                Encoding.UTF8, 
                "application/json");
                
            // Add correlation ID header
            if (contextAccessor.HttpContext != null)
            {
                var correlationId = contextAccessor.GetCorrelationId();
                if (correlationId.HasValue)
                {
                    httpClient.DefaultRequestHeaders.Remove("X-Correlation-ID");
                    httpClient.DefaultRequestHeaders.Add("X-Correlation-ID", correlationId.Value.ToString());
                }
            }
            
            var response = await httpClient.PutAsync(requestUri, requestBody);
            var jsonResult = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Error when calling Product service to update product. Error was {StatusCode} {@responseBody}", 
                    (int)response.StatusCode, jsonResult);
                throw new PassthroughException(response);
            }

            logger.LogInformation("Successfully updated product {@merchantId}", product.MerchantId);
            return jsonResult;
        }
    }

    public async Task<string> UpdateProductStatusAsync(Product product)
    {
        var requestUri = $"{ProductServiceBaseUrl}{string.Format(UpdateProductStatusEndpoint, product.MerchantId)}";
        
        using (logger.BeginScope("UpdateProductStatusAsync({@requestUri})", requestUri))
        {
            logger.LogInformation("Calling Product service to update product status {@merchantId}", product.MerchantId);
            
            var requestBody = new StringContent(
                JsonConvert.SerializeObject(product), 
                Encoding.UTF8, 
                "application/json");
                
            // Add correlation ID header
            if (contextAccessor.HttpContext != null)
            {
                var correlationId = contextAccessor.GetCorrelationId();
                if (correlationId.HasValue)
                {
                    httpClient.DefaultRequestHeaders.Remove("X-Correlation-ID");
                    httpClient.DefaultRequestHeaders.Add("X-Correlation-ID", correlationId.Value.ToString());
                }
            }
            
            var response = await httpClient.PatchAsync(requestUri, requestBody);
            var jsonResult = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Error when calling Product service to update product status. Error was {StatusCode} {@responseBody}", 
                    (int)response.StatusCode, jsonResult);
                throw new PassthroughException(response);
            }

            logger.LogInformation("Successfully updated product status {@merchantId}", product.MerchantId);
            return jsonResult;
        }
    }

    public async Task<string> SendFileAsync(Product product)
    {
        var requestUri = $"{ProductServiceBaseUrl}{string.Format(SendFileEndpoint, product.MerchantId)}";
        
        using (logger.BeginScope("SendFileAsync({@requestUri})", requestUri))
        {
            logger.LogInformation("Calling Product service to send file {@merchantId}", product.MerchantId);
            
            var requestBody = new StringContent(
                JsonConvert.SerializeObject(product), 
                Encoding.UTF8, 
                "application/json");
                
            // Add correlation ID header
            if (contextAccessor.HttpContext != null)
            {
                var correlationId = contextAccessor.GetCorrelationId();
                if (correlationId.HasValue)
                {
                    httpClient.DefaultRequestHeaders.Remove("X-Correlation-ID");
                    httpClient.DefaultRequestHeaders.Add("X-Correlation-ID", correlationId.Value.ToString());
                }
            }
            
            var response = await httpClient.PostAsync(requestUri, requestBody);
            var jsonResult = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Error when calling Product service to send file. Error was {StatusCode} {@responseBody}", 
                    (int)response.StatusCode, jsonResult);
                throw new PassthroughException(response);
            }

            logger.LogInformation("Successfully sent file {@merchantId}", product.MerchantId);
            return jsonResult;
        }
    }
}
