trigger:
- dev
- test
- release/*
- uae-dev
- uae-test

pool:
  name: 'GD-Azure' 

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'
  build.MajorVersion: 1
  build.MinorVersion: 1

name: $(build.MajorVersion).$(build.MinorVersion).$(rev:r)

resources:
  repositories:
    - repository: devops
      type: git
      name: GeideaPaymentGateway/DevOps.Scripts

steps:
- checkout: self
  persistCredentials: true

- template: config-service.yml@devops

- task: Bash@3
  displayName: 'Check if the the source branch is allowed for the PR'
  inputs:
   targetType: 'inline'
   script: |
     echo
     echo 'The only and only source branch allowed for merging into UAE-TEST is the UAE-DEV branch!!!'
     echo
     exit 1
  condition: and(ne(variables['System.PullRequest.SourceBranch'], 'refs/heads/uae-dev'), contains(variables['System.PullRequest.TargetBranch'], 'refs/heads/uae-test'))

- task: NuGetAuthenticate@0
- task: NuGetToolInstaller@1

- task: UseDotNet@2
  displayName: 'Use .Net SDK 5.0.x'
  inputs:
    packageType: 'sdk'
    version: 5.0.x
    installationPath: $(Agent.TempDirectory)/dotnet

- task: UseDotNet@2
  displayName: 'Use .Net SDK 6.0.x'
  enabled: true
  inputs:
    packageType: 'sdk'
    version: 6.0.x
    installationPath: $(Agent.TempDirectory)/dotnet

- task: DotNetCoreCLI@2
  displayName: Restore NuGet packages
  inputs:
    command: 'restore'
    projects: '**/*.sln'
    feedsToUse: 'config'
    nugetConfigPath: '$(Build.SourcesDirectory)/nuget.config'
    noCache: true

- task: DotnetGlobalToolInstaller@0
  displayName: 'Install dotnet-format global tool'
  inputs:
    name: 'dotnet-format'
    checkLatest: true

- task: DotNetCoreCLI@2
  displayName: 'Check code formatting'
  inputs:
    command: 'custom'
    projects: '**/*.sln'
    custom: 'format'
    ##arguments: '--verify-no-changes'    

- task: DotNetCoreCLI@2
  displayName: Build
  inputs:
    command: build
    projects: |
        **/*.sln
    arguments: --configuration $(BuildConfiguration) --no-restore -p:Version=$(Build.BuildNumber)

- task: DotNetCoreCLI@2
  displayName: Run unit tests
  inputs:
    command: test
    projects: |
        **/*.Test*.csproj
    arguments: --configuration $(BuildConfiguration) --no-build /p:CollectCoverage=true /p:CoverletOutputFormat=opencover /p:IncludeTestAssembly=false
- bash: |
   curl -fLsS -o bridge.zip 'https://sig-repo.synopsys.com/artifactory/bds-integrations-release/com/synopsys/integration/synopsys-bridge/latest/synopsys-bridge-linux64.zip'  && unzip -qo -d $(Agent.TempDirectory) bridge.zip && rm -f bridge.zip
   $(Agent.TempDirectory)/synopsys-bridge --verbose --stage srm
  env:
   BRIDGE_SRM_URL: 'https://srm.gd-azure-dev.net'
   BRIDGE_SRM_APIKEY: 'api-key:_Y05VeSJZG0ID9I2YRF3ysPbj_Fh1z9Jl_RbIelP'
   BRIDGE_SRM_ASSESSMENT_TYPES: 'SAST,SCA'
   BRIDGE_SRM_PROJECT_NAME: $(Build.Repository.Name)
   BRIDGE_SRM_BRANCH_NAME: $(Build.SourceBranchName)
   BRIDGE_SRM_BRANCH_PARENT: dev
  displayName: 'SRM Scan'
  continueOnError: true
     
- task: DotNetCoreCLI@2
  displayName: 'Publish'
  inputs:
    command: 'publish'
    publishWebProjects: true
    arguments: '--configuration Release -p:Version=$(Build.BuildNumber) --output "$(build.artifactstagingdirectory)" --no-cache --runtime=linux-musl-x64 --source "https://pkgs.dev.azure.com/GeideaPaymentGateway/_packaging/f50ad5dc-f078-4d5c-a6d3-d384c0414d73/nuget/v3/index.json"'
    zipAfterPublish: false

- task: Docker@2
  displayName: Docker - build image
  inputs:
    containerRegistry: geidea
    command: build
    buildContext: '$(build.artifactstagingdirectory)/Geidea.NexusBridgeAPI'
    repository: nbapi-service
    tags: |
     $(Build.SourceBranchName)-$(Build.BuildNumber)

- task: Bash@3
  displayName: "Scan the docker image with trivy"
  inputs:
    targetType: 'inline'
    script: |
      trivy image --exit-code 0 --severity LOW,MEDIUM geidea.azurecr.io/nbapi-service:$(Build.SourceBranchName)-$(Build.BuildNumber)
      trivy image --exit-code 1 --severity HIGH,CRITICAL geidea.azurecr.io/nbapi-service:$(Build.SourceBranchName)-$(Build.BuildNumber)

- task: Docker@2
  displayName: Docker - push image
  inputs:
    containerRegistry: geidea
    command: push
    repository: nbapi-service
    tags: |
     $(Build.SourceBranchName)-$(Build.BuildNumber)
  condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))

- task: GitTag@5
  inputs:
    workingdir: '$(SYSTEM.DEFAULTWORKINGDIRECTORY)'
    tag: '$(Build.BuildNumber)'
    tagMessage: '$(Build.BuildNumber)'
  condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
  