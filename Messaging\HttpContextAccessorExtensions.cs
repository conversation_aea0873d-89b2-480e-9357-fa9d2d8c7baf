﻿using Microsoft.AspNetCore.Http;

namespace Services.Messaging;

public static class HttpContextAccessorExtensions
{
    public const string CorrelationIdHeaderName = "X-Correlation-ID";

    public static Guid? GetCorrelationId(this IHttpContextAccessor contextAccessor)
    {
        if (contextAccessor == null || contextAccessor.HttpContext?.Request?.Headers == null)
            return null;

        if (!contextAccessor.HttpContext.Request.Headers.TryGetValue(CorrelationIdHeaderName, out var correlationIdValue))
            return null;

        return Guid.TryParse(correlationIdValue, out var correlationId) ? correlationId : null;
    }
}
