﻿using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Services.Models;
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Services;

public class RequestLogService : IRequestLogService
{
    private readonly HttpClient httpClient;
    private readonly IOptionsMonitor<UrlSettings> urlOptions;
    private readonly ILogger<RequestLogService> logger;
    private string LogServiceBaseUrl => urlOptions.CurrentValue?.RequestLogServiceBaseUrl ?? string.Empty;

    private const string LogServiceEndpoint = "/api/v1/RequestLog";
    public RequestLogService(HttpClient httpClient, IOptionsMonitor<UrlSettings> urlOptions, ILogger<RequestLogService> logger)
    {
        this.httpClient = httpClient;
        this.urlOptions = urlOptions;
        this.logger = logger;
    }

    public async Task CreateRequestLog(RequestLog log)
    {
        var requestUri = $"{LogServiceBaseUrl}{LogServiceEndpoint}";

        using (logger.BeginScope("CreateRequestLogAsync({@requestUri})", requestUri))
        {
            logger.LogInformation("Calling Request Log service to log the request Information");
            var requestLog = new StringContent(JsonConvert.SerializeObject(log), Encoding.UTF8, "application/json");
            var   response = await httpClient.PostAsync(requestUri, requestLog);

            var jsonResult = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling Request Log service to create Log Information. Error was {StatusCode} {@responseBody}",(int)response.StatusCode, jsonResult);
                throw new PassthroughException(response);
            }
        }
    }
}