﻿using Common.Services;
using Geidea.Utils.Policies.Evaluation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Geidea.NexusBridgeAPI.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class BankController : ControllerBase
{
    private readonly Authorized authorized;
    private readonly IBankService bankService;
    private readonly ILogger<BankController> logger;


    public BankController(Authorized authorized, ILogger<BankController> logger, IBankService bankService)
    {
        this.logger = logger;
        this.authorized = authorized;
        this.bankService = bankService;
    }
}
