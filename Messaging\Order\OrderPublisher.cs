﻿using Geidea.Messages.Merchant;
using Geidea.Messages.Merchant.Messages;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;
using System.Text.Json;
using System.Threading.Channels;
using Messaging.Models;
using Common.Models;
using Common.Models.v1;
using Geidea.Messages.Base;

namespace Services.Messaging;

public class OrderPublisher : OrderMessageClient, IOrderPublisher
{
    private const string OrderUpdateRoutingKey = "NexusBridge.Order.Update";
    private const string UpdateStatusRoutingKey = "NexusBridge.Order.UpdateStatus";
    private const string OrderCreateRoutingKey = "NexusBridge.Order.Create";
    private const string ExchangeName = "NexusBridge.Order.Recieve";

    private readonly IHttpContextAccessor contextAccessor;
    private readonly ILogger<OrderPublisher> logger;

    public OrderPublisher(IConnectionFactory connectionFactory, ILogger<OrderPublisher> logger, IHttpContextAccessor contextAccessor)
        : base(connectionFactory)
    {
        this.logger = logger;
        this.contextAccessor = contextAccessor;
    }

    public void Connect()
    {
        SetupConnection();
        SetupChannel();
    }

    public void PublishOrderCreatedEvent(Common.Models.v1.Merchant order)
    {
        var message = new GenericMessage<Common.Models.v1.Merchant>
        {
            Header = BuildHeader(OrderCreateRoutingKey, contextAccessor.GetCorrelationId(), order.Counterparty!),
            Data = order
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(ExchangeName, OrderCreateRoutingKey, messageAsByteArray, logger);
    }

    public void PublishOrderUpdatedEvent(Order order)
    {
        var message = new OrderUpdateEvent
        {
            Header = BuildHeader(OrderUpdateRoutingKey, contextAccessor.GetCorrelationId()),
            Order = order
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(ExchangeName, OrderUpdateRoutingKey, messageAsByteArray, logger);
    }

    public void PublishUpdateStatusEvent(Order order)
    {
        var message = new OrderUpdateEvent
        {
            Header = BuildHeader(UpdateStatusRoutingKey, contextAccessor.GetCorrelationId()),
            Order = order
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(ExchangeName, UpdateStatusRoutingKey, messageAsByteArray, logger);
    }



    private void SetupChannel()
    {
        Channel.ExchangeDeclare(ExchangeName, ExchangeType.Fanout, true);
    }
}