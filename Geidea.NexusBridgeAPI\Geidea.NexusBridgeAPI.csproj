﻿<Project Sdk="Microsoft.NET.Sdk.Web">
	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>enable</Nullable>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<CheckForOverflowUnderflow>True</CheckForOverflowUnderflow>
		<ProjectGuid>{2a6a7d61-5d62-4723-aa00-f29605957459}</ProjectGuid>
		<LangVersion>latest</LangVersion>
		<RootNamespace>Geidea.NexusBridgeAPI</RootNamespace>
		<AssemblyName>Geidea.NexusBridgeAPI</AssemblyName>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
		<PackageReference Include="DinkToPdfCopyDependencies" Version="1.0.8" />
		<PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.19.0" />
		<PackageReference Include="FluentValidation.AspNetCore" Version="11.2.2" />
		<PackageReference Include="Geidea.PaymentGateway.ConfigServiceClient" Version="3.1.123" />
		<PackageReference Include="Geidea.Utils.Counterparty" Version="2.0.232" />
		<PackageReference Include="Geidea.Utils.Exceptions" Version="1.1.330" />
		<PackageReference Include="Geidea.Utils.HeaderValidation" Version="2.0.222" />
		<PackageReference Include="Geidea.Utils.HealthChecks" Version="1.1.342" />
		<PackageReference Include="Geidea.Utils.Messaging" Version="1.0.32" />
		<PackageReference Include="Geidea.Utils.Policies" Version="2.0.306" />
		<PackageReference Include="Geidea.Utils.Security" Version="2.0.137" />
		<PackageReference Include="Geidea.Utils.Swagger" Version="1.1.256" />
		<PackageReference Include="Geidea.Utils.UserInfo" Version="1.0.159" />
		<PackageReference Include="Geidea.Utils.Versioning" Version="1.1.247" />
		<PackageReference Include="GeideaPaymentGateway.Utils.CommonModels" Version="3.0.39" />
		<PackageReference Include="Microsoft.AspNetCore.HeaderPropagation" Version="6.0.12" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.12" />
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="6.0.11" />
		<PackageReference Include="Serilog.Formatting.Compact" Version="1.1.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.4.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.6" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.4.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Common\Common.csproj" />
		<ProjectReference Include="..\Services\Services.csproj" />
	</ItemGroup>
</Project>