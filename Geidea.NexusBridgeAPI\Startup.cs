using Common.Options;
using Common.Services;
using Geidea.NexusBridgeAPI.Extensions;
using Geidea.NexusBridgeAPI.Filters;
using Geidea.PaymentGateway.ConfigServiceClient;
using Geidea.Utils.Cleanup;
using Geidea.Utils.Counterparty;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Geidea.Utils.HeaderValidation;
using Geidea.Utils.HeaderValidation.Extensions;
using Geidea.Utils.HealthChecks;
using Geidea.Utils.HttpClientExtensions;
using Geidea.Utils.Logging;
using Geidea.Utils.Messaging.Base;
using Geidea.Utils.ReferenceData;
using Geidea.Utils.Security;
using Geidea.Utils.Swagger;
using Geidea.Utils.UserInfo;
using Geidea.Utils.Validation;
using Geidea.Utils.Versioning;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Services;
using Swashbuckle.AspNetCore.Filters;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Reflection;
using static GeideaPaymentGateway.Utils.CommonModels.Constants;
using Constants = Geidea.Utils.Common.Constants;
using IConnectionFactory = Services.Messaging.IConnectionFactory;

namespace Geidea.NexusBridgeAPI;

public class Startup
{
    public Startup(IConfiguration configuration)
    {
        this.Configuration = configuration;
    }

    public IConfiguration Configuration { get; }

    public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IOptionsMonitor<ApplicationOptions> appOptions, ILogger<Startup> logger)
    {
        app.UseHeaderPropagation();

        app.UseMiddleware<SerilogRequestLogger>();
        app.UseMiddleware<ExceptionHandler>();
        app.UseMiddleware<HeadersValidationMiddleware>();
        app.UseMiddleware<CounterpartyHandler>();

        Console.WriteLine("Environment: " + env.EnvironmentName);

        if (!env.IsProduction())
        {
            app.UseForwardedHeaders(new ForwardedHeadersOptions
            {
                ForwardedHeaders = ForwardedHeaders.XForwardedProto
            });
            app.UseSwagger(c =>
            {
                c.PreSerializeFilters.Add((swaggerDoc, httpReq) => swaggerDoc.Servers = new List<OpenApiServer> {
                    new OpenApiServer { Url = $"{httpReq.Scheme}://{httpReq.Host.Value}{(httpReq.Headers["GD-Service"].Count > 0 ? "/NexusBridge" : null)}"}
                     });
            });

            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("swagger/v1/swagger.json", "Geidea.NexusBridge API v1");
                c.RoutePrefix = string.Empty; 
            });
        }
        app.UseRouting();
        app.UseCors(a =>
        {
            a.AllowAnyHeader();
            a.AllowAnyMethod();
            a.WithExposedHeaders(CorrelationIdHeaderName);

            if (!string.IsNullOrWhiteSpace(appOptions.CurrentValue.CorsAllowedOrigins))
            {
                var allowedOrigins = appOptions.CurrentValue.CorsAllowedOrigins
                    .Split(",", StringSplitOptions.RemoveEmptyEntries)
                    .Select(o => o.Trim())
                    .ToArray();

                logger.LogInformation("CORS: Allowing origins specified in the 'CorsAllowedOrigins' configuration property: {@allowedOrigins}",
                    string.Join(',', allowedOrigins));
                a.WithOrigins(allowedOrigins);
            }
        });

        app.UseVersionMiddleware();
        app.UseInternalConfigurationView();

        app.UseHealthChecks("/health", new HealthCheckOptions { AllowCachingResponses = false, ResponseWriter = HealthResponseWriter.WriteHealthCheckResponse });
        app.UseAuthentication();
        app.UseAuthorization();
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
        });


    }

    public void ConfigureServices(IServiceCollection services)
    {
        services.AddStructuredLogging(Configuration);
        services.AddApplicationOptions(Configuration);

        services.AddIdentityProviderConfiguration(Configuration);
        services.AddAuthorization();

        // Add HTTP clients for API communication
        services.AddHttpClient<IMerchantApiClient, Services.HttpClients.MerchantApiClient>()
            .AddHeaderPropagation();
        services.AddHttpClient<IOrderApiClient, Services.HttpClients.OrderApiClient>()
            .AddHeaderPropagation();
        services.AddHttpClient<IProductApiClient, Services.HttpClients.ProductApiClient>()
            .AddHeaderPropagation();

        services.AddStructuredLogging(Configuration);
        services.AddHealthCheckServicesAndOptions(Configuration);
        services.AddAutoMapper(typeof(Startup));
        services.Configure<ApiBehaviorOptions>(options => options.SuppressModelStateInvalidFilter = false);
        services.AddHealthCheckServicesAndOptions(Configuration);
        services.AddHealthChecks();

        services.AddPollyHttpClient<IReferenceDataClient, ReferenceDataClient>(Configuration).AddHeaderPropagation();

        services.AddTransient<IMerchantService, MerchantService>();
        services.AddTransient<IBankService, BankService>();
        services.AddTransient<IDueDilligenceService, DueDilligenceService>();
        services.AddTransient<IOrderService, OrderService>();
        services.AddTransient<IProductService, ProductService>();
        services.AddTransient<ICampaignService, CampaignService>();
        services.AddTransient<IRequestLogService, RequestLogService>();

        services.AddTransient<ICleanupService, CleanupService>();
        services.AddScoped<ICounterpartyProvider, CounterpartyProvider>();

        services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();

        services.AddStructuredLogging(Configuration);
        services.AddHealthCheckServicesAndOptions(Configuration);
        services.AddHealthChecks()
         .AddCommonHealthChecks();

        ValidationHelpers.InitValidation();
        services.AddControllers().AddNewtonsoftJson()
            .AddMvcOptions(options =>
            {
                options.ModelValidatorProviders.Add(new InputModelSanitizationValidatorProvider());
            });

        services.AddSwaggerGen(options =>
        {
            options.SwaggerDoc("v1", new OpenApiInfo
            {
                Version = "v1",
                Title = "Geidea.NexusBridge API v1",
                Description = "Geidea.NexusBridge API v1"
            });

            options.ExampleFilters();
            options.EnableAnnotations();
            options.OperationFilter<CounterpartyHeaderFilter>();
            options.DocumentFilter<CommonSwaggerDocument>();

            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            options.IncludeXmlComments(xmlPath);

            var securityScheme = new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Id = "Bearer",
                    Type = ReferenceType.SecurityScheme
                },
                Description = "JWT Token",
                In = ParameterLocation.Header,
                Name = "Authorization",
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer"
            };

            options.AddSecurityDefinition("Bearer", securityScheme);
            options.AddSecurityRequirement(new OpenApiSecurityRequirement {
                { securityScheme, new List<string>() }
            });
        });
        services.AddSwaggerExamplesFromAssemblyOf<Startup>();
        services.AddAuthorizationPolicies(Configuration);
        services.AddIUserInfoService(Configuration);
        services.AddIReferenceDataClient(Configuration);
        services.AddHeaderWhitelistServiceCollection(Configuration);

        services.AddHeaderPropagation(o =>
        {
            o.Headers.Add(Constants.CounterpartyHeaderName);
            o.Headers.Add(Constants.CounterpartyHeaderName, _ => Constants.CounterpartyUae);
            o.Headers.Add("Authorization");
        });

        ValidationHelpers.InitValidation();
    }
}