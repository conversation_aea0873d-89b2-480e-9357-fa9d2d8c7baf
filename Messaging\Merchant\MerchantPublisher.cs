﻿using Common.Models.v1;
using Geidea.Messages.Base;
using Geidea.Messages.Merchant.Messages;
using Messaging.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;
using System.Text.Json;
using System.Threading.Channels;

namespace Services.Messaging;

public class MerchantPublisher : MessageClient, IMerchantPublisher
{
    private const string MerchantUpdated = "NexusBridge.Merchant.Update";
    private const string MerchantDeleted = "NexusBridge.Merchant.Delete";
    private const string MerchantCreated = "NexusBridge.Merchant.Create";
    private const string MerchantRetry = "NexusBridge.Merchant.Retry";

    private const string ExchangeName = "NexusBridge.Merchant.Exchange";
    private const string RetryExchangeName = "NexusBridge.Merchant.ExchangeRetry";


    private readonly IHttpContextAccessor contextAccessor;
    private readonly ILogger<MerchantPublisher> logger;

    public MerchantPublisher(IConnectionFactory connectionFactory, ILogger<MerchantPublisher> logger, IHttpContextAccessor contextAccessor)
        : base(connectionFactory)
    {
        this.logger = logger;
        this.contextAccessor = contextAccessor;
    }

    public void Connect()
    {
        SetupConnection();
        SetupChannel();
    }

    public void PublishMerchantCreatedEvent(Merchant merchant)
    {
        var message = new GenericMessage<Merchant>
        {
            Header = BuildHeader(MerchantCreated, contextAccessor.GetCorrelationId(), merchant.Counterparty!),
            Data = merchant    
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(ExchangeName, MerchantCreated, messageAsByteArray, logger);
    }

    public void PublishMerchantUpdatedEvent(Merchant merchant)
    {
        var message = new GenericMessage<Merchant>
        {
            Header = BuildHeader(MerchantCreated, contextAccessor.GetCorrelationId(), merchant.Counterparty!),
            Data = merchant,

        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(ExchangeName, MerchantUpdated, messageAsByteArray, logger);
    }

    public void PublishMerchantUpdateStatusEvent(Guid merchantId)
    {
        var message = new MerchantDeletedEvent
        {
            Header = BuildHeader(MerchantDeleted, contextAccessor.GetCorrelationId()),
            MerchantId = merchantId
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(ExchangeName, MerchantDeleted, messageAsByteArray, logger);
    }

    

    private void SetupChannel()
    {
        Channel.ExchangeDeclare(ExchangeName, ExchangeType.Fanout, true);
    }
}