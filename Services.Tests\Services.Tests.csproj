﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<LangVersion>latest</LangVersion>
		<Nullable>enable</Nullable>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<CheckForOverflowUnderflow>True</CheckForOverflowUnderflow>
		<ProjectGuid>{25AC4F3A-1B8D-45C9-ABAA-8138E6C5463E}</ProjectGuid>
	</PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.0" />
    <PackageReference Include="coverlet.msbuild" Version="3.2.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="FluentAssertions" Version="6.8.0" />
    <PackageReference Include="NSubstitute" Version="4.4.0" />
    <PackageReference Include="nunit" Version="3.13.3" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.3.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.4.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Services\Services.csproj" />
  </ItemGroup>

</Project>
