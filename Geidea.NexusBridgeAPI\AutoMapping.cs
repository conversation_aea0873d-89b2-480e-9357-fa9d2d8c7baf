﻿using AutoMapper;
using Common.Models.v1;

namespace Geidea.NexusBridgeAPI;

public class AutoMapping : Profile
{
    public AutoMapping()
    {
        CreateMap<Merchant, Merchant>();
        CreateMap<MerchantAddress, MerchantAddress>();
        CreateMap<MerchantAccountConfig, MerchantAccountConfig>();
        CreateMap<MerchantExternalProduct, MerchantExternalProduct>();
        CreateMap<MerchantBankAccountResponse, MerchantBankAccountResponse>();
        CreateMap<MerchantCommissionConfig, MerchantCommissionConfig>();
    }
}