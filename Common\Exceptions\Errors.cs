﻿namespace Common.Exceptions;

public static class Errors
{
    private const string App = "NB_";

    public static (string Message, string Code) ApplicationId_length => ("Application Id length sholuld not greater than 36 characters", App + "ApplicationId_length");
    public static (string Message, string Code) Email_Address_Empty => ("The email address should not be empty", App + "Email_Address_Empty");
    public static (string Message, string Code) Email_Address_Invalid => ("Invalid email address", App + "Email_Address_Invalid");
    public static (string Message, string Code) Email_Address_MaxLength => ("Email length validation failed.", App + "EmailMaxLength");
    public static (string Message, string Code) URL_Invalid => ("Invalid website url", App + "URL_Invalid");
    public static (string Message, string Code) CountryWithDigit_Invalid => ("Invalid country", App + "CountryWithDigit_Invalid");
    public static (string Message, string Code) CityWithDigit_Invalid => ("Invalid city", App + "CityWithDigit_Invalid");
    public static (string Message, string Code) CityKeyWithAlphabet_Invalid => ("Invalid city key", App + "CityKeyWithAlphabet_Invalid");
    public static (string Message, string Code) ZipRequired => ("Zip Code is required", App + "ZipRequired");
    public static (string Message, string Code) ZipInvalid => ("Invalid zip", App + "Zip_Invalid");
    public static (string Message, string Code) InvalidPhoneNumber => ("Invalid phone number format", App + "InvalidPhoneNumber");
    public static (string Message, string Code) PhoneNumberMaxLength => ("Phone number length validation failed.", App + "PhoneNumberMaxLength");
    public static (string Message, string Code) BusinessTypeMaxLength => ("Business type length validation failed.", App + "BusinessTypeMaxLength");
    public static (string Message, string Code) RegistrationNumberMaxLength => ("Registration number length validation failed.", App + "RegistrationNumberMaxLength");
    public static (string Message, string Code) MunicipalLicenseNumberMaxLength => ("Municipal license number length validation failed.", App + "MunicipalLicenseNumberMaxLength");
    public static (string Message, string Code) TextMaxLength => ("field exceeds max length of ", App + "TextMaxLength");
    public static (string Message, string Code) TextMinLength => ("field length can not be less than ", App + "TextMinLength");
    public static (string Message, string Code) AlphaNumericFailed => ("field contains special symbols, only AlphaNumeric is allowed.", App + "AlphaNumericFailed");
    public static (string Message, string Code) TextRequired => ("field can not be empty.", App + "TextRequired");
    public static (string Message, string Code) PasswordInvalid => ("Invalid Password. (Minimum 8 characters at least 1 Uppercase Alphabet, 1 Lowercase Alphabet, 1 Number and 1 Special Character)", App + "PasswordInvalid");
    public static (string Message, string Code) PasswordMaxLength => ("Password max length validation failed. Allowed Max Length: ", App + "PasswordMaxLength");
    public static (string Message, string Code) PasswordMinLength => ("Password min length validation failed. Allowed Min Length: ", App + "PasswordMinLength");
    public static (string Message, string Code) InvalidWholeNumber => ("field contains invalid number", App + "InvalidWholeNumber");
    public static (string Message, string Code) General_InvalidCountryPrefix => ("Invalid country prefix format", App + "General_InvalidCountryPrefix");
    public static (string Message, string Code) WrongCountryPrefix => ("Country prefix does not belong to the country", App + "WrongCountryPrefix");
    public static (string Message, string Code) InvalidDecimalNumber => ("field contains invalid decimal number ", App + "InvalidDecimalNumber");
}