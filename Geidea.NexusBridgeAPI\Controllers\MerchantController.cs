using Common.Models;
using Common.Models.v1;
using Common.Services;
using Geidea.Utils.Policies.Evaluation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Geidea.NexusBridgeAPI.Controllers;

[Authorize]
[ApiController]
[Route("api/v1/[controller]")]
public class MerchantController : ControllerBase
{
    private readonly Authorized authorized;
    private readonly IMerchantService merchantService;
    private readonly ILogger<MerchantController> logger;

    public MerchantController(IMerchantService merchantService, Authorized authorized, ILogger<MerchantController> logger)
    {
        this.logger = logger;
        this.authorized = authorized;
        this.merchantService = merchantService;
    }

    /// <summary>
    /// Create Merchant Message
    /// </summary>
    /// <response code="200">Returns a successful message sent</response>
    /// <response code="400">Returns the error.</response>
    /// <response code="401">If the request in unauthorized.</response>
    /// <response code="403">If the user does not have the correct role.</response>
    [HttpPost("create")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> CreateMerchant([FromBody] Merchant merchant)
    {

        logger.LogInformation("Recieved Merchant create action in NexusBridgeAPI for {@merchantId}",merchant.MerchantId);

        //var personOfInterest = merchant.PersonOfInterests.SingleOrDefault(x => x.IsPrincipal);

        //if (!await authorized.To.Create.Business(personOfInterest!.MerchantPersonOfInterestId))
        //{
        //    return Forbid();
        //}
        var result = await merchantService.CreateMerchantMessageAsync(merchant);

        return Ok(result);
    }

    /// <summary>
    /// Update Merchant Message
    /// </summary>
    /// <response code="200">Returns a successful message sent</response>
    /// <response code="400">Returns the error.</response>
    /// <response code="401">If the request in unauthorized.</response>
    /// <response code="403">If the user does not have the correct role.</response>
    [HttpPatch("update")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> UpdateMerchant([FromBody] Merchant merchant)
    {

        
        var personOfInterest = merchant.PersonOfInterests.Where(x => x.IsPrincipal).SingleOrDefault();

        if (!await authorized.To.Create.Business(personOfInterest!.MerchantPersonOfInterestId))
        {
            return Forbid();
        }
        var result = await merchantService.UpdateMerchantMessageAsync(merchant);
        return Ok(result);
    }

    /// <summary>
    /// Delete Merchant Message
    /// </summary>
    /// <response code="200">Returns a successful message sent</response>
    /// <response code="400">Returns the error.</response>
    /// <response code="401">If the request in unauthorized.</response>
    /// <response code="403">If the user does not have the correct role.</response>
    [HttpPatch("updatestatus")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> UpdateMerchantStatus(Guid merchantId)
    {
        var result = await merchantService.UpdateMerchantStatusMessageAsync(merchantId);
        return Ok(result);
    }

    ///// <summary>
    ///// Create Merchant addresss Message
    ///// </summary>
    ///// <response code="200">Returns a successful message sent</response>
    ///// <response code="400">Returns the error.</response>
    ///// <response code="401">If the request in unauthorized.</response>
    ///// <response code="403">If the user does not have the correct role.</response>
    //[HttpPost("createaddress")]
    //[ProducesResponseType(StatusCodes.Status200OK)]
    //[ProducesResponseType(StatusCodes.Status400BadRequest)]
    //[ProducesResponseType(StatusCodes.Status401Unauthorized)]
    //[ProducesResponseType(StatusCodes.Status403Forbidden)]
    //public async Task<IActionResult> CreateMerchantAddress([FromBody] MerchantAddress address)
    //{
    //    var result = await merchantService.CreateMerchantAddressMessageAsync(address);
    //    return Ok(result);
    //}

    ///// <summary>
    ///// patch Merchant addresss Message
    ///// </summary>
    ///// <response code="200">Returns a successful message sent</response>
    ///// <response code="400">Returns the error.</response>
    ///// <response code="401">If the request in unauthorized.</response>
    ///// <response code="403">If the user does not have the correct role.</response>
    //[HttpPatch("updateaddress")]
    //[ProducesResponseType(StatusCodes.Status200OK)]
    //[ProducesResponseType(StatusCodes.Status400BadRequest)]
    //[ProducesResponseType(StatusCodes.Status401Unauthorized)]
    //[ProducesResponseType(StatusCodes.Status403Forbidden)]
    //public async Task<IActionResult> UpdateMerchantAddress([FromBody] MerchantAddress address)
    //{
    //    var result = await merchantService.UpdateMerchantAddressMessageAsync(address);
    //    return Ok(result);
    //}

    ///// <summary>
    ///// Delete Merchant addresss Message
    ///// </summary>
    ///// <response code="200">Returns a successful message sent</response>
    ///// <response code="400">Returns the error.</response>
    ///// <response code="401">If the request in unauthorized.</response>
    ///// <response code="403">If the user does not have the correct role.</response>
    //[HttpDelete("deleteaddress")]
    //[ProducesResponseType(StatusCodes.Status200OK)]
    //[ProducesResponseType(StatusCodes.Status400BadRequest)]
    //[ProducesResponseType(StatusCodes.Status401Unauthorized)]
    //[ProducesResponseType(StatusCodes.Status403Forbidden)]
    //public async Task<IActionResult> DeleteMerchantAddress([FromBody] MerchantAddressDeleteRequest request)
    //{
    //    var result = await merchantService.DeleteMerchantAddressMessageAsync(request);
    //    return Ok(result);
    //}


    ///// <summary>
    ///// Create Meeza Merchant Registration message
    ///// </summary>
    ///// <response code="200">Returns a successful message sent</response>
    ///// <response code="400">Returns the error.</response>
    ///// <response code="401">If the request in unauthorized.</response>
    ///// <response code="403">If the user does not have the correct role.</response>
    //[HttpPost("MeezaMerchantRegistration")]
    //[ProducesResponseType(StatusCodes.Status200OK)]
    //[ProducesResponseType(StatusCodes.Status400BadRequest)]
    //[ProducesResponseType(StatusCodes.Status401Unauthorized)]
    //[ProducesResponseType(StatusCodes.Status403Forbidden)]
    //public async Task<IActionResult> MeezaMerchantRegistration([FromBody] IReadOnlyCollection<Guid> storeIds, Guid merchantId, string counterparty)
    //{
    //    var result = await merchantService.PublishMeezaMerchantRegistrationEvent(storeIds, merchantId, counterparty);
    //    return Ok(result);
    //}


    ///// <summary>
    ///// Create Store Gateway Configuration message
    ///// </summary>
    ///// <response code="200">Returns a successful message sent</response>
    ///// <response code="400">Returns the error.</response>
    ///// <response code="401">If the request in unauthorized.</response>
    ///// <response code="403">If the user does not have the correct role.</response>
    //[HttpPost("storegateway")]
    //[ProducesResponseType(StatusCodes.Status200OK)]
    //[ProducesResponseType(StatusCodes.Status400BadRequest)]
    //[ProducesResponseType(StatusCodes.Status401Unauthorized)]
    //[ProducesResponseType(StatusCodes.Status403Forbidden)]
    //public async Task<IActionResult> StoreGatewayConfigurationCreate([FromBody] StoreGatewayConfig config)
    //{
    //    var result = await merchantService.PublishStoreGatewayConfigurationCreateEvent(config);
    //    return Ok(result);
    //}
}