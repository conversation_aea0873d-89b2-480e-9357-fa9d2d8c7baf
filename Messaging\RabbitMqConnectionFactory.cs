﻿using GeideaPaymentGateway.Utils.RabbitMQ;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;

namespace Services.Messaging;

public class RabbitMqConnectionFactory : IConnectionFactory
{
    private readonly IOptionsMonitor<RabbitMqConfig> configMonitor;

    private RabbitMqConfig Config => configMonitor.CurrentValue;

    public RabbitMqConnectionFactory(IOptionsMonitor<RabbitMqConfig> configMonitor)
    {
        this.configMonitor = configMonitor;
    }

    public IConnection CreateConnection()
    {
        return RabbitMqHelper.CreateConnectionFactory(Config).CreateConnection();
    }
}