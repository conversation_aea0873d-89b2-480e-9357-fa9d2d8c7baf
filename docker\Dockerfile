# Use .NET 6.0 runtime image based on Alpine Linux
FROM mcr.microsoft.com/dotnet/aspnet:6.0-alpine

# Install necessary dependencies
RUN apk add --upgrade --no-cache  bash icu-libs krb5-libs
# Set environment variable to enable globalization
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
# Set working directory inside the container
WORKDIR /app
# Expose port 80 to allow external connections
EXPOSE 80
# Copy all files from the current directory (where <PERSON><PERSON><PERSON><PERSON> is located) to /app in the container
COPY . /app
# Set the entry point for the container to execute the .NET application
ENTRYPOINT ["dotnet","./Geidea.NexusBridgeAPI.dll"]