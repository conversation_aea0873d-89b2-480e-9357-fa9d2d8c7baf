﻿using Common.Exceptions;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace Common.Validators
{
    public class PasswordValidator : AbstractValidator<string>
    {
        public PasswordValidator([Optional] int? MaxLength, [Optional] int? MinLength)
        {
            // ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&]{8,}/ (Minimum 8 characters at least 1 Uppercase Alphabet, 1 Lowercase Alphabet, 1 Number and 1 Special Character)

            RuleFor(x => x)
                .Matches(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&]{8,}/")
                .WithErrorCode(Errors.PasswordInvalid.Code)
                .WithMessage(Errors.PasswordInvalid.Message);

            if (MaxLength > 0 && MaxLength != null)
            {
                RuleFor(x => x)
                .MaximumLength(Convert.ToInt32(MaxLength))
                .WithErrorCode(Errors.PasswordMaxLength.Code)
                .WithMessage(Errors.PasswordMaxLength.Message + Convert.ToString(MaxLength));
            }

            if (MinLength != 0 && MinLength != null)
            {
                RuleFor(x => x)
                .MaximumLength(Convert.ToInt32(MinLength))
                .WithErrorCode(Errors.PasswordMinLength.Code)
                .WithMessage(Errors.PasswordMinLength.Message + Convert.ToString(MinLength));
            }

        }
    }
}
