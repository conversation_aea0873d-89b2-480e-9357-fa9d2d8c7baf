﻿using Common.Exceptions;
using FluentValidation;

namespace Common.Validators
{
    public class PhoneNumberStringValidator:AbstractValidator<string>
    {
        public PhoneNumberStringValidator()
        {
            RuleFor(x => x)
                .NotEmpty()
                .WithErrorCode(Errors.InvalidPhoneNumber.Code)
                .WithMessage(Errors.InvalidPhoneNumber.Message)
                .Must(x=> x !=null && x.Length <=16)
                .WithErrorCode(Errors.PhoneNumberMaxLength.Code)
                .WithMessage(Errors.PhoneNumberMaxLength.Message);
        }
    }
}
