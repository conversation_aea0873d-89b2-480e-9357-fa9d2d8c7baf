﻿using Common.Models;
using Common.Services;
using Geidea.Utils.Policies.Evaluation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Services;
using System.Threading.Tasks;

namespace Geidea.NexusBridgeAPI.Controllers;

[Authorize]
[ApiController]
[Route("api/v1/[controller]")]
public class ProductController : ControllerBase
{
    private readonly Authorized authorized;
    private readonly IProductService productService;
    private readonly ILogger<ProductController> logger;

    public ProductController(Authorized authorized, ILogger<ProductController> logger, IProductService productService)
    {
        this.logger = logger;
        this.authorized = authorized;
        this.productService = productService;
    }

    /// <summary>
    /// Create Order Message
    /// </summary>
    /// <response code="200">Returns a successful message sent</response>
    /// <response code="400">Returns the error.</response>
    /// <response code="401">If the request in unauthorized.</response>
    /// <response code="403">If the user does not have the correct role.</response>
    [HttpPost("create")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> CreateProduct([FromBody] Product product)
    {
        var result = await productService.CreateProductMessageAsync(product);
        return Ok(result);
    }

    /// <summary>
    /// Update Order Message
    /// </summary>
    /// <response code="200">Returns a successful message sent</response>
    /// <response code="400">Returns the error.</response>
    /// <response code="401">If the request in unauthorized.</response>
    /// <response code="403">If the user does not have the correct role.</response>
    [HttpPatch("update")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> UpdateProduct([FromBody] Product product)
    {
        var result = await productService.UpdateProductMessageAsync(product);
        return Ok(result);
    }

    /// <summary>
    /// Update Status Message
    /// </summary>
    /// <response code="200">Returns a successful message sent</response>
    /// <response code="400">Returns the error.</response>
    /// <response code="401">If the request in unauthorized.</response>
    /// <response code="403">If the user does not have the correct role.</response>
    [HttpPatch("updateproductstatus")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> UpdateProductStatus([FromBody] Product product)
    {
        var result = await productService.UpdateProductStatusAsync(product);
        return Ok(result);
    }

    /// <summary>
    /// Update Status Message
    /// </summary>
    /// <response code="200">Returns a successful message sent</response>
    /// <response code="400">Returns the error.</response>
    /// <response code="401">If the request in unauthorized.</response>
    /// <response code="403">If the user does not have the correct role.</response>
    [HttpPatch("sendfile")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> SendFile([FromBody] Product product)
    {
        var result = await productService.SendFileMessageAsync(product);
        return Ok(result);
    }
}