﻿using Geidea.Messages.Base;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;

namespace Services.Messaging;

public abstract class OrderMessageClient : IDisposable
{
    private const string OrderService = "OrderService";
    private const string ExchangeName = "NexusBridge.Order.Recieve";
    private const string QueueName = "NexusBridge.Order.Recieve";

    private readonly IConnectionFactory connectionFactory;
    private IConnection? connection;

    protected IModel? Channel { get; private set; }

    private bool disposed;

    protected OrderMessageClient(IConnectionFactory connectionFactory)
    {
        this.connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
        SetupConnection();
        SetupChannel();
    }

    protected void SetupConnection()
    {
        if (disposed)
        {
            throw new ObjectDisposedException(nameof(OrderMessageClient));
        }

        connection = connectionFactory.CreateConnection();
        Channel = connection.CreateModel();
    }

    private void SetupChannel()
    {
        //Channel!.QueueDeclare(queue: QueueName, durable: false, exclusive: false, autoDelete: false, arguments: null);
        Channel.ExchangeDeclare(ExchangeName, ExchangeType.Direct, true);

    }

    protected static Header BuildHeader(string type, Guid? correlationId, string counterparty = "") => new()
    {
        Type = type,
        Id = Guid.NewGuid(),
        Version = 3,
        Sender = OrderService,
        CreatedDate = DateTime.UtcNow,
        CorrelationId = correlationId,
        Counterparty = counterparty
    };

    protected void Send(string exchangeName, string routingKey, byte[] messageBody, ILogger logger)
    {
        try
        {
            //Channel!.QueueDeclare(queue: routingKey, durable: false, exclusive: false, autoDelete: false, arguments: null);
            Channel.BasicPublish(exchangeName, routingKey, null, messageBody);
        }
        catch (Exception ex)
        {
            logger.LogWarning("Unable to send order data message to RabbitMQ {0}", ex);
        }
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!disposed)
        {
            if (disposing)
            {
                Channel?.Close();
                connection?.Close();

                Channel?.Dispose();
                connection?.Dispose();
            }

            Channel = null;
            connection = null;
            disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}