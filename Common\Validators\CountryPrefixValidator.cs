﻿namespace Common.Validators
{
    using Common.Exceptions;
    using FluentValidation;
    using static Common.Constants;
    using UtilsConstants = Geidea.Utils.Common.Constants;

    public class CountryPrefixValidator : AbstractValidator<string>
    {
        public CountryPrefixValidator(string counterparty)
        {
            RuleFor(x => x).Matches(@"(?:^)\+[0-9]{1,3}(?:$)")
                .WithErrorCode(Errors.General_InvalidCountryPrefix.Code).WithMessage(Errors.General_InvalidCountryPrefix.Message);

            if (counterparty == UtilsConstants.CounterpartySaudi)
            {
                RuleFor(x => x).Equal(CountryPrefixType.CountryPrefixSaudi)
                    .WithErrorCode(Errors.WrongCountryPrefix.Code).WithMessage(Errors.WrongCountryPrefix.Message);
            }
            else if (counterparty == UtilsConstants.CounterpartyEgypt)
            {
                RuleFor(x => x).Equal(CountryPrefixType.CountryPrefixEgypt)
                    .WithErrorCode(Errors.WrongCountryPrefix.Code).WithMessage(Errors.WrongCountryPrefix.Message);
            }
            else if (counterparty == UtilsConstants.CounterpartyUae)
            {
                RuleFor(x => x).Equal(CountryPrefixType.CountryPrefixUae)
                    .WithErrorCode(Errors.WrongCountryPrefix.Code).WithMessage(Errors.WrongCountryPrefix.Message);
            }
        }
    }
}
