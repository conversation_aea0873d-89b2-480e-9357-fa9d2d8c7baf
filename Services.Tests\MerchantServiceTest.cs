﻿using AutoMapper;
using Common.Services;
using Geidea.Messages.Base;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using RabbitMQ.Client.Events;
using Services.Messaging;
using Services.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Tests
{
    public class MerchantServiceTest
    {
        private MerchantService messageClient;
        private readonly IMapper mapper = Substitute.For<IMapper>();
        private readonly IMerchantPublisher msg = Substitute.For<IMerchantPublisher>();
        private readonly IRequestLogService reuestlogService = Substitute.For<IRequestLogService>();
        private readonly IHttpContextAccessor accessor = Substitute.For<IHttpContextAccessor>();
        private readonly ILogger<MerchantService> logger = Substitute.For<ILogger<MerchantService>>();

        public MerchantServiceTest()
        {

            messageClient = new MerchantService(mapper, msg, reuestlogService,  accessor, logger);
        }

        [Test]
        public Task CreateMessage_WhenMerchantCreateTriggered_ShouldThrowExceptionAsync()
        {
            var merchantCreate = new Common.Models.v1.Merchant
            {
                    MerchantId = Guid.NewGuid(),
                    LeadId = Guid.NewGuid(),
                    MerchantType = "Retail",
                    MerchantStatus = "Active",
                    Tag = "High-Value",
                    Counterparty = "GEIDEA_UAE",
                    CreatedBy = "AdminUser",
                    UpdatedBy = null,
                    CreatedDate = DateTime.UtcNow,
                    UpdatedDate = null,
                    DeletedFlag = false,
                    ApplicationId = "App123"

            };

            //var result= await messageClient.CreateMerchantMessageAsync(merchantCreate);
            var result = Assert.ThrowsAsync<ServiceException>(async () => await messageClient.CreateMerchantMessageAsync(merchantCreate));

            Assert.NotNull(result);
            return Task.CompletedTask;
        }
        [Test]
        public Task UpdateMessage_WhenMerchantUpdateTriggered_ShouldThrowExceptionAsync()
        {
            var merchantCreate = new Common.Models.v1.Merchant
            {


                MerchantId = Guid.NewGuid(),
                LeadId = Guid.NewGuid(),
                MerchantType = "Retail",
                MerchantStatus = "Active",
                Tag = "High-Value",
                Counterparty = "GEIDEA_UAE",
                CreatedBy = "AdminUser",
                UpdatedBy = null,
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = null,
                DeletedFlag = false,
                ApplicationId = "App123"

            };

            var result = Assert.ThrowsAsync<ServiceException>(async () => await messageClient.UpdateMerchantMessageAsync(merchantCreate));
            Assert.NotNull(result);
           return Task.CompletedTask;


        }
        [Test]
        public Task UpdateStatus_WhenMerchantUpdateStatusTriggered_ShouldThrowExceptionAsync()
        {
            var merchantId = Guid.NewGuid();
            var result = Assert.ThrowsAsync<ServiceException>(async () => await messageClient.UpdateMerchantStatusMessageAsync(merchantId));
            Assert.NotNull(result);
            return Task.CompletedTask;
        }
    }
}
