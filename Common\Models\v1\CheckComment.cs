﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.v1
{
    public class CheckComment
    {
        public Guid CommentId { get; set; }

        public Guid? MerchantCheckId { get; set; }
        public Guid? ShareHolderCompanyCheckId { get; set; }

        public Guid? PersonCheckId { get; set; }

        public string CommentText { get; set; } = string.Empty;

        public string CreatedBy { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; }

        public string? UpdatedBy { get; set; }

        public DateTime? UpdatedDate { get; set; }

        public string AuthorName { get; set; } = string.Empty;

        public string AuthorRole { get; set; } = string.Empty;
    }
}
