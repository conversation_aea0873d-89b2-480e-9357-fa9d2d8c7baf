﻿using Geidea.Messages.Merchant;
using Geidea.Messages.Merchant.Messages;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;
using System.Text.Json;
using System.Threading.Channels;
using Messaging.Models;
using Common.Models;
using Geidea.Messages.Base;
namespace Services.Messaging;

public class ProductPublisher : ProductMessageClient, IProductPublisher
{
    private const string ProductUpdated = "NexusBridge.Product.Update";
    private const string StatusUpdated = "NexusBridge.Product.UpdateStatus";
    private const string ProductCreated = "NexusBridge.Product.Create";
    private const string SendFile = "NexusBridge.Product.SendFile";
    private const string ExchangeName = "NexusBridge.Product.Recieve";

    private readonly IHttpContextAccessor contextAccessor;
    private readonly ILogger<ProductPublisher> logger;

    public ProductPublisher(IConnectionFactory connectionFactory, ILogger<ProductPublisher> logger, IHttpContextAccessor contextAccessor)
        : base(connectionFactory)
    {
        this.logger = logger;
        this.contextAccessor = contextAccessor;
    }

    public void Connect()
    {
        SetupConnection();
        SetupChannel();
    }

    public void PublishProductCreatedEvent(Product product)
    {
        var message = new GenericMessage<Common.Models.Product>
        {
            Header = BuildHeader(ProductCreated, contextAccessor.GetCorrelationId()),
            Data = product
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(ExchangeName, ProductCreated, messageAsByteArray, logger);
    }

    public void PublishProductUpdatedEvent(Product product)
    {
        var message = new ProductUpdateEvent
        {
            Header = BuildHeader(ProductUpdated, contextAccessor.GetCorrelationId()),
            Product = product
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(ExchangeName, ProductUpdated, messageAsByteArray, logger);
    }

    public void PublishUpdateProductStatusEvent(Product product)
    {
        var message = new ProductUpdateEvent
        {
            Header = BuildHeader(StatusUpdated, contextAccessor.GetCorrelationId()),
            Product = product
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(ExchangeName, StatusUpdated, messageAsByteArray, logger);
    }
    public void PublishSendFileEvent(Product product)
    {
        var message = new ProductSendFileEvent
        {
            Header = BuildHeader(SendFile, contextAccessor.GetCorrelationId()),
            Product = product
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(ExchangeName, SendFile, messageAsByteArray, logger);
    }


    private void SetupChannel()
    {
        Channel.ExchangeDeclare(ExchangeName, ExchangeType.Fanout, true);
    }
}