﻿using Common.Exceptions;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Validators
{
    internal class DecimalValidator : AbstractValidator<string>
    {
        public DecimalValidator(bool IsRequired, string FieldName)
        {
            if (IsRequired)
            {
                RuleFor(x => x)
                .NotEmpty()
                .WithErrorCode(Errors.TextRequired.Code)
                .WithMessage(FieldName + " " + Errors.TextRequired.Message);
            }
            RuleFor(x => x)
                .Matches(@"^-?(0|[1-9]\d*)(\.\d+)?$")
                .WithErrorCode(FieldName + " " + Errors.InvalidDecimalNumber.Code)
                .WithMessage(Errors.InvalidDecimalNumber.Message);

        }
    }
}
