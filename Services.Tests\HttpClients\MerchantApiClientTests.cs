using Common.Models.v1;
using Common.Options;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using Services.HttpClients;
using System;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Services.Tests.HttpClients;

[TestFixture]
public class MerchantApiClientTests
{
    private Mock<IOptionsMonitor<UrlSettings>> _urlOptionsMonitor;
    private Mock<ILogger<MerchantApiClient>> _logger;
    private Mock<IHttpContextAccessor> _httpContextAccessor;
    private Mock<HttpMessageHandler> _httpMessageHandler;
    private HttpClient _httpClient;
    private MerchantApiClient _merchantApiClient;
    private UrlSettings _urlSettings;

    [SetUp]
    public void Setup()
    {
        _urlOptionsMonitor = new Mock<IOptionsMonitor<UrlSettings>>();
        _logger = new Mock<ILogger<MerchantApiClient>>();
        _httpContextAccessor = new Mock<IHttpContextAccessor>();
        _httpMessageHandler = new Mock<HttpMessageHandler>();
        
        _urlSettings = new UrlSettings
        {
            MerchantServiceBaseUrl = "https://test-merchant-api.com"
        };
        
        _urlOptionsMonitor.Setup(x => x.CurrentValue).Returns(_urlSettings);
        
        _httpClient = new HttpClient(_httpMessageHandler.Object);
        _merchantApiClient = new MerchantApiClient(_httpClient, _urlOptionsMonitor.Object, _logger.Object, _httpContextAccessor.Object);
    }

    [Test]
    public async Task CreateMerchantAsync_WhenSuccessful_ReturnsSuccessMessage()
    {
        // Arrange
        var merchant = new Merchant
        {
            MerchantId = Guid.NewGuid(),
            MerchantDetails = new MerchantDetails { BusinessId = "TEST123" }
        };

        var expectedResponse = "Merchant created successfully";
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(expectedResponse)
        };

        _httpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _merchantApiClient.CreateMerchantAsync(merchant);

        // Assert
        Assert.AreEqual(expectedResponse, result);
        
        _httpMessageHandler.Protected().Verify(
            "SendAsync",
            Times.Once(),
            ItExpr.Is<HttpRequestMessage>(req => 
                req.Method == HttpMethod.Post && 
                req.RequestUri.ToString() == "https://test-merchant-api.com/api/v1/merchants"),
            ItExpr.IsAny<CancellationToken>());
    }

    [Test]
    public async Task UpdateMerchantAsync_WhenSuccessful_ReturnsSuccessMessage()
    {
        // Arrange
        var merchant = new Merchant
        {
            MerchantId = Guid.NewGuid(),
            MerchantDetails = new MerchantDetails { BusinessId = "TEST123" }
        };

        var expectedResponse = "Merchant updated successfully";
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(expectedResponse)
        };

        _httpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _merchantApiClient.UpdateMerchantAsync(merchant);

        // Assert
        Assert.AreEqual(expectedResponse, result);
        
        _httpMessageHandler.Protected().Verify(
            "SendAsync",
            Times.Once(),
            ItExpr.Is<HttpRequestMessage>(req => 
                req.Method == HttpMethod.Put && 
                req.RequestUri.ToString() == $"https://test-merchant-api.com/api/v1/merchants/{merchant.MerchantId}"),
            ItExpr.IsAny<CancellationToken>());
    }

    [Test]
    public async Task UpdateMerchantStatusAsync_WhenSuccessful_ReturnsSuccessMessage()
    {
        // Arrange
        var merchantId = Guid.NewGuid();
        var expectedResponse = "Merchant status updated successfully";
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(expectedResponse)
        };

        _httpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _merchantApiClient.UpdateMerchantStatusAsync(merchantId);

        // Assert
        Assert.AreEqual(expectedResponse, result);
        
        _httpMessageHandler.Protected().Verify(
            "SendAsync",
            Times.Once(),
            ItExpr.Is<HttpRequestMessage>(req => 
                req.Method == HttpMethod.Patch && 
                req.RequestUri.ToString() == $"https://test-merchant-api.com/api/v1/merchants/{merchantId}/status"),
            ItExpr.IsAny<CancellationToken>());
    }

    [TearDown]
    public void TearDown()
    {
        _httpClient?.Dispose();
    }
}
