﻿using Geidea.Messages.Base;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;

namespace Services.Messaging;

public abstract class MessageClient : IDisposable
{
    private const string MerchantService = "MerchantService";
    private const string ExchangeName = "NexusBridge.Merchant.Recieve";
    private const string QueueName = "NexusBridge.Merchant.Recieve";

    private readonly IConnectionFactory connectionFactory;
    private IConnection? connection;

    protected IModel? Channel { get; private set; }

    private bool disposed;

    protected MessageClient(IConnectionFactory connectionFactory)
    {
        this.connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
        SetupConnection();
        SetupChannel();
    }

    protected void SetupConnection()
    {
        if (disposed)
        {
            throw new ObjectDisposedException(nameof(MessageClient));
        }

        connection = connectionFactory.CreateConnection();
        Channel = connection.CreateModel();
    }

    private void SetupChannel()
    {
        //Channel!.QueueDeclare(queue: QueueName, durable: false, exclusive: false, autoDelete: false, arguments: null);
        Channel.ExchangeDeclare(ExchangeName, ExchangeType.Direct, true);

    }

    protected static Header BuildHeader(string type, Guid? correlationId, string counterparty = "") => new()
    {
        Type = type,
        Id = Guid.NewGuid(),
        Version = 3,
        Sender = MerchantService,
        CreatedDate = DateTime.UtcNow,
        CorrelationId = correlationId,
        Counterparty = counterparty
    };

    protected void Send(string exchangeName, string routingKey, byte[] messageBody, ILogger logger)
    {
        try
        {
            Channel.BasicPublish(ExchangeName, routingKey, null, messageBody);
        }
        catch (Exception ex)
        {
            logger.LogWarning("Failed  to send  message to RabbitMQ for {0} : NBAPIException {1}", routingKey,ex);
        }
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!disposed)
        {
            if (disposing)
            {
                Channel?.Close();
                connection?.Close();

                Channel?.Dispose();
                connection?.Dispose();
            }

            Channel = null;
            connection = null;
            disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}