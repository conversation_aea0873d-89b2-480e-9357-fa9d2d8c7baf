﻿using System;

namespace Common.Models;

public class MerchantDetails
{
    public Guid MerchantDetailsId { get; set; }

    public Guid MerchantId { get; set; }

    public string? BusinessId { get; set; } = string.Empty;

    public string? BusinessType { get; set; }
    public string? CompanyPhoneNumber { get; set; }
    public string? MerchantSegment { get; set; }



    public string? OutletType { get; set; }

    public string? MCC { get; set; }

    public string? BusinessDomain { get; set; }

    public string? LegalName { get; set; }

    public string? LegalNameAr { get; set; }

    public string? TradingName { get; set; }

    public string? Nickname { get; set; }

    public string? TradingCurrency { get; set; }

    public string? RegistrationNumber { get; set; }

    public string? VatNumber { get; set; }

    public DateTime FoundationDate { get; set; }

    public string? MunicipalLicenseNumber { get; set; }

    public string? AcquirerReview { get; set; }

    public string? AcquiringLedger { get; set; }

    public bool VatAppliedFlag { get; set; }

    public bool TaxExempt { get; set; }

    public string? AdditionalTradingInformation { get; set; }

    public string? MIDMerchantReference { get; set; }

    public string? Website { get; set; }

    public decimal AnnualTurnover { get; set; }

    public string? Region { get; set; }

    public string? DefaultLanguage { get; set; }

    public string? UnifiedId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? UpdatedDate { get; set; }

    public string CreatedBy { get; set; } = string.Empty;

    public string? UpdatedBy { get; set; } = string.Empty;

    public string? CityCr { get; set; }

    public string? AddressCr { get; set; }

    public string? MerchantName { get; set; }

    public string? ReferralChannel { get; set; }
    public string? Mid { get; set; }
    public string? BusinessEmail { get; set; }
    public string? ChannelType { get; set; }
    public string? PayoutBank { get; set; }
    public int PayoutMinimumCap { get; set; }
    public int PayoutTransferFeeAmount { get; set; }
    public DateTime TLIssueDate { get; set; }
    public DateTime TLExpiryDate { get; set; }
    public int MaxMonthlyTransaction { get; set; }
    public int HighestSingleTransaction { get; set; }
    public int salesAgent { get; set; }
    public int salesManager { get; set; }
    public string? Iban { get; set; }
    public string? AccountNumber { get; set; }
    public string? BeneficiaryFullName { get; set; }
    public string? ParentMerchantId { get; set; }
    public string? AlternativeMerchantName { get; set; }
    public string? StoreName { get; set; }
    public string? AlternativeStoreName { get; set; }
    public string? RiskLevel { get; set; }
}