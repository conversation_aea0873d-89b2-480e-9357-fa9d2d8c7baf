﻿using AutoMapper;
using Common.Models;
using Common.Services;
using Newtonsoft.Json;
using Services.Models;
using System.Text;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Geidea.Utils.Security.Claims;

namespace Services;

public class ProductService : IProductService
{

    private readonly IMapper mapper;
    private readonly IProductApiClient productApiClient;
    private readonly IRequestLogService requestLogService;
    private readonly ILogger<ProductService> logger;
    private readonly IHttpContextAccessor contextAccessor;
    public ProductService(IMapper mapper, IProductApiClient productApiClient, IRequestLogService requestLogService, ILogger<ProductService> logger, IHttpContextAccessor contextAccessor)
    {
        this.mapper = mapper;
        this.productApiClient = productApiClient;
        this.requestLogService = requestLogService;
        this.logger = logger;
        this.contextAccessor = contextAccessor;
    }
    public async Task<string> CreateProductMessageAsync(Product product)
    {
        var msg = mapper.Map<Product>(product);
        var apiResponse = await productApiClient.CreateProductAsync(product);
        await requestLogService.CreateRequestLog(new RequestLog()
        {
            CorrelationId = contextAccessor.GetCorrelationId(),
            ParentCorrelationId = Guid.Empty,
            Entity = Common.Constants.Entity.Product,
            EntityId = string.Empty,
            EntryDate = DateTime.Now,
            RequestId = string.Empty,
            Status = Common.Constants.MerchantEnablementStatus.Published,
            RequestMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(product))),
            ResponseMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(apiResponse)),
            RequestType = Common.Constants.Actions.Create,
            UserId = GetCurrentUserId().ToString(),
        });
        return "Product create request sent to Product API successfully";
    }
    public async Task<string> UpdateProductMessageAsync(Product product)
    {
        var msg = mapper.Map<Product>(product);

        var apiResponse = await productApiClient.UpdateProductAsync(product);
        return "Update Product request sent to Product API successfully";
    }
    public async Task<string> UpdateProductStatusAsync(Product product)
    {
        var msg = mapper.Map<Product>(product);

        var apiResponse = await productApiClient.UpdateProductStatusAsync(product);
        return "Update Product status sent to Product API successfully";
    }
    public async Task<string> SendFileMessageAsync(Product product)
    {
        var msg = mapper.Map<Product>(product);

        var apiResponse = await productApiClient.SendFileAsync(product);
        return "Send File sent to Product API successfully";
    }

    private Guid GetCurrentUserId()
    {
        return ClaimsPrincipalExtensions.GetUserIdFromClaims(contextAccessor.HttpContext!.User.Claims);
    }
}
