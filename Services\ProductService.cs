﻿using AutoMapper;
using Common.Models;
using Common.Services;
using Newtonsoft.Json;
using Services.Messaging;
using Services.Models;
using System.Text;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Geidea.Utils.Security.Claims;

namespace Services;

public class ProductService : IProductService
{

    private readonly IMapper mapper;
    private readonly IProductPublisher productPublisher;
    private readonly IRequestLogService requestLogService;
    private readonly ILogger<ProductService> logger;
    private readonly IHttpContextAccessor contextAccessor;
    public ProductService(IMapper mapper, IProductPublisher productPublisher, IRequestLogService requestLogService, ILogger<ProductService> logger, IHttpContextAccessor contextAccessor)
    {
        this.mapper = mapper;
        this.productPublisher = productPublisher;
        this.requestLogService = requestLogService;
        this.logger = logger;
        this.contextAccessor = contextAccessor;
    }
    public async Task<string> CreateProductMessageAsync(Product product)
    {
        var msg = mapper.Map<Product>(product);
        await Task.Run(() => productPublisher.PublishProductCreatedEvent(product));
        await requestLogService.CreateRequestLog(new RequestLog()
        {
            CorrelationId = contextAccessor.GetCorrelationId(),
            ParentCorrelationId = Guid.Empty,
            Entity = Common.Constants.Entity.Product,
            EntityId = string.Empty,
            EntryDate = DateTime.Now,
            RequestId = string.Empty,
            Status = Common.Constants.MerchantEnablementStatus.Published,
            RequestMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(product))),
            ResponseMetaData = string.Empty,
            RequestType = Common.Constants.Actions.Create,
            UserId = GetCurrentUserId().ToString(),
        });
        return " Product create request sent to RabitMQ successfully";
    }
    public async Task<string> UpdateProductMessageAsync(Product product)
    {
        var msg = mapper.Map<Product>(product);

        await Task.Run(() => productPublisher.PublishProductUpdatedEvent(product));
        return "Update Product request sent to RabitMQ successfully";
    }
    public async Task<string> UpdateProductStatusAsync(Product product)
    {
        var msg = mapper.Map<Product>(product);

        await Task.Run(() => productPublisher.PublishUpdateProductStatusEvent(product));
        return "Update Product status sent to RabitMQ successfully";
    }
    public async Task<string> SendFileMessageAsync(Product product)
    {
        var msg = mapper.Map<Product>(product);

        await Task.Run(() => productPublisher.PublishSendFileEvent(product));
        return "Send File sent to RabitMQ successfully";
    }

    private Guid GetCurrentUserId()
    {
        return ClaimsPrincipalExtensions.GetUserIdFromClaims(contextAccessor.HttpContext!.User.Claims);
    }
}
